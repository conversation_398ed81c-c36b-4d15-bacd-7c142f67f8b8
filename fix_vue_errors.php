<?php

/**
 * Vue.js错误修复工具
 * 专门解决"Cannot access 'xt' before initialization"等Vue初始化错误
 */

class VueErrorsFixer
{
    private $projectRoot;
    private $fixes = [];
    private $errors = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'FIX' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 检查Vue.js文件完整性
     */
    private function checkVueFiles()
    {
        $this->log('INFO', '检查Vue.js文件完整性...');
        
        $adminAssetsPath = $this->projectRoot . '/public/admin/assets';
        $vueFiles = [
            'vue-vendor-B18IO7kr.js' => 'Vue核心库',
            'element-plus-BNplCcqW.js' => 'Element Plus组件库',
            'index-BWOuGn0N.js' => '应用主文件'
        ];
        
        $allFilesExist = true;
        foreach ($vueFiles as $file => $description) {
            $filePath = $adminAssetsPath . '/' . $file;
            if (file_exists($filePath)) {
                $fileSize = filesize($filePath);
                $this->log('SUCCESS', "{$description}: {$file} (大小: " . number_format($fileSize) . " 字节)");
                
                // 检查文件是否为空或损坏
                if ($fileSize < 1000) {
                    $this->log('WARN', "{$description}文件可能损坏或不完整");
                    $this->errors[] = "{$description}文件可能损坏";
                    $allFilesExist = false;
                }
            } else {
                $this->log('ERROR', "{$description}文件不存在: {$file}");
                $this->errors[] = "{$description}文件缺失";
                $allFilesExist = false;
            }
        }
        
        return $allFilesExist;
    }
    
    /**
     * 检查index.html文件配置
     */
    private function checkIndexHtml()
    {
        $this->log('INFO', '检查index.html配置...');
        
        $indexFile = $this->projectRoot . '/public/admin/index.html';
        if (!file_exists($indexFile)) {
            $this->log('ERROR', 'index.html文件不存在');
            $this->errors[] = 'index.html文件缺失';
            return false;
        }
        
        $content = file_get_contents($indexFile);
        
        // 检查关键脚本标签
        $requiredScripts = [
            '/admin/assets/vue-vendor-B18IO7kr.js',
            '/admin/assets/element-plus-BNplCcqW.js',
            '/admin/assets/index-BWOuGn0N.js'
        ];
        
        $allScriptsFound = true;
        foreach ($requiredScripts as $script) {
            if (strpos($content, $script) !== false) {
                $this->log('SUCCESS', "发现脚本引用: {$script}");
            } else {
                $this->log('ERROR', "缺少脚本引用: {$script}");
                $this->errors[] = "缺少脚本引用: {$script}";
                $allScriptsFound = false;
            }
        }
        
        // 检查是否有app容器
        if (strpos($content, 'id="app"') !== false) {
            $this->log('SUCCESS', '发现Vue应用容器: #app');
        } else {
            $this->log('ERROR', '缺少Vue应用容器: #app');
            $this->errors[] = '缺少Vue应用容器';
            $allScriptsFound = false;
        }
        
        return $allScriptsFound;
    }
    
    /**
     * 修复Vue初始化顺序问题
     */
    private function fixVueInitializationOrder()
    {
        $this->log('FIX', '修复Vue初始化顺序问题...');
        
        $indexFile = $this->projectRoot . '/public/admin/index.html';
        if (!file_exists($indexFile)) {
            return false;
        }
        
        $content = file_get_contents($indexFile);
        $originalContent = $content;
        
        // 检查是否需要添加defer属性
        $needsDefer = false;
        if (strpos($content, 'crossorigin src=') !== false && strpos($content, 'defer') === false) {
            $needsDefer = true;
        }
        
        if ($needsDefer) {
            // 为主要脚本添加defer属性
            $content = preg_replace(
                '/(<script[^>]*type="module"[^>]*crossorigin[^>]*)(>)/',
                '$1 defer$2',
                $content
            );
            
            if ($content !== $originalContent) {
                // 备份原文件
                $backupFile = $indexFile . '.backup.' . date('YmdHis');
                copy($indexFile, $backupFile);
                $this->log('INFO', "已备份index.html到: " . basename($backupFile));
                
                // 写入修复后的内容
                if (file_put_contents($indexFile, $content)) {
                    $this->log('SUCCESS', '已为脚本添加defer属性');
                    $this->fixes[] = '修复Vue脚本加载顺序';
                    return true;
                } else {
                    $this->log('ERROR', '无法写入修复后的index.html');
                    return false;
                }
            }
        }
        
        $this->log('INFO', 'Vue初始化顺序无需修复');
        return true;
    }
    
    /**
     * 创建Vue错误处理脚本
     */
    private function createErrorHandlingScript()
    {
        $this->log('FIX', '创建Vue错误处理脚本...');
        
        $errorHandlerScript = $this->projectRoot . '/public/admin/vue-error-handler.js';
        
        $scriptContent = <<<'JS'
// Vue.js错误处理脚本
// 用于捕获和处理Vue初始化错误

(function() {
    'use strict';
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        
        // 检查是否是Vue相关错误
        if (event.error && event.error.message) {
            const message = event.error.message;
            
            if (message.includes('Cannot access') && message.includes('before initialization')) {
                console.warn('检测到Vue初始化顺序错误，尝试重新加载...');
                
                // 延迟重新加载，给其他脚本时间完成加载
                setTimeout(function() {
                    if (!window.Vue || !window.Vue.createApp) {
                        console.log('Vue未正确加载，刷新页面...');
                        window.location.reload();
                    }
                }, 2000);
            }
        }
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
    });
    
    // Vue特定错误处理
    document.addEventListener('DOMContentLoaded', function() {
        // 检查Vue是否正确加载
        setTimeout(function() {
            if (typeof window.Vue === 'undefined') {
                console.error('Vue未加载，可能存在脚本加载问题');
                
                // 显示错误信息给用户
                const appElement = document.getElementById('app');
                if (appElement && !appElement.innerHTML.trim()) {
                    appElement.innerHTML = `
                        <div style="
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            flex-direction: column;
                            font-family: Arial, sans-serif;
                            background: #f5f5f5;
                        ">
                            <div style="
                                background: white;
                                padding: 40px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                text-align: center;
                                max-width: 500px;
                            ">
                                <h2 style="color: #e74c3c; margin-bottom: 20px;">
                                    🔧 系统正在加载中...
                                </h2>
                                <p style="color: #666; margin-bottom: 20px;">
                                    前端资源正在初始化，请稍候片刻
                                </p>
                                <button onclick="window.location.reload()" style="
                                    background: #3498db;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                ">
                                    刷新页面
                                </button>
                            </div>
                        </div>
                    `;
                }
            }
        }, 3000);
    });
    
    console.log('Vue错误处理脚本已加载');
})();
JS;
        
        if (file_put_contents($errorHandlerScript, $scriptContent)) {
            $this->log('SUCCESS', '创建Vue错误处理脚本成功');
            $this->fixes[] = '创建Vue错误处理脚本';
            
            // 将错误处理脚本添加到index.html
            $this->addErrorHandlerToIndex();
            
            return true;
        } else {
            $this->log('ERROR', '创建Vue错误处理脚本失败');
            return false;
        }
    }
    
    /**
     * 将错误处理脚本添加到index.html
     */
    private function addErrorHandlerToIndex()
    {
        $indexFile = $this->projectRoot . '/public/admin/index.html';
        if (!file_exists($indexFile)) {
            return false;
        }
        
        $content = file_get_contents($indexFile);
        
        // 检查是否已经添加了错误处理脚本
        if (strpos($content, 'vue-error-handler.js') !== false) {
            $this->log('INFO', '错误处理脚本已存在于index.html中');
            return true;
        }
        
        // 在head标签中添加错误处理脚本
        $errorHandlerTag = '    <script src="/admin/vue-error-handler.js"></script>';
        $content = str_replace('</head>', $errorHandlerTag . "\n  </head>", $content);
        
        if (file_put_contents($indexFile, $content)) {
            $this->log('SUCCESS', '已将错误处理脚本添加到index.html');
            $this->fixes[] = '添加错误处理脚本到index.html';
            return true;
        } else {
            $this->log('ERROR', '无法修改index.html');
            return false;
        }
    }
    
    /**
     * 清理浏览器缓存相关的响应头
     */
    private function addCacheControlHeaders()
    {
        $this->log('FIX', '添加缓存控制配置...');
        
        $htaccessFile = $this->projectRoot . '/public/.htaccess';
        if (!file_exists($htaccessFile)) {
            $this->log('WARN', '.htaccess文件不存在');
            return false;
        }
        
        $content = file_get_contents($htaccessFile);
        
        // 检查是否已经有缓存控制规则
        if (strpos($content, 'Cache-Control') !== false) {
            $this->log('INFO', '缓存控制规则已存在');
            return true;
        }
        
        // 添加缓存控制规则
        $cacheRules = <<<'HTACCESS'

# Vue.js资源缓存控制
<IfModule mod_headers.c>
    # 对于JS和CSS文件，设置较短的缓存时间以便调试
    <FilesMatch "\.(js|css)$">
        Header set Cache-Control "no-cache, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # 对于HTML文件，禁用缓存
    <FilesMatch "\.html$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>
HTACCESS;
        
        $content .= $cacheRules;
        
        if (file_put_contents($htaccessFile, $content)) {
            $this->log('SUCCESS', '添加缓存控制规则成功');
            $this->fixes[] = '添加缓存控制规则';
            return true;
        } else {
            $this->log('ERROR', '无法修改.htaccess文件');
            return false;
        }
    }
    
    /**
     * 运行修复流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔧 Vue.js错误修复工具                             ║\n";
        echo "║                                                          ║\n";
        echo "║    专门解决Vue初始化错误和页面空白问题                    ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 执行修复步骤
        $vueFilesOk = $this->checkVueFiles();
        $indexHtmlOk = $this->checkIndexHtml();
        
        if ($vueFilesOk && $indexHtmlOk) {
            $this->fixVueInitializationOrder();
            $this->createErrorHandlingScript();
            $this->addCacheControlHeaders();
        }
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 显示修复结果
        echo "\n";
        echo "============================================================\n";
        echo "                    修复结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if (empty($this->errors)) {
            echo "🎉 Vue.js错误修复完成！\n";
        } else {
            echo "⚠️  修复完成，但存在 " . count($this->errors) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 修复统计:\n";
        echo "   修复项目: " . count($this->fixes) . " 个\n";
        echo "   发现问题: " . count($this->errors) . " 个\n";
        echo "   修复耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->errors as $error) {
                echo "   - {$error}\n";
            }
            echo "\n";
        }
        
        echo "🚀 下一步操作:\n";
        if (empty($this->errors)) {
            echo "   1. 强制刷新浏览器 (Ctrl+F5 或 Cmd+Shift+R)\n";
            echo "   2. 清除浏览器缓存和Cookie\n";
            echo "   3. 访问管理后台: https://f.fcwan.cn/admin\n";
            echo "   4. 检查浏览器控制台是否还有错误\n";
        } else {
            echo "   1. 解决上述问题\n";
            echo "   2. 重新构建前端项目\n";
            echo "   3. 重新运行修复工具\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 如果问题仍然存在，可能需要重新构建admin前端项目。\n";
        echo "\n";
        
        return empty($this->errors);
    }
}

// 运行修复工具
if (php_sapi_name() === 'cli') {
    $fixer = new VueErrorsFixer();
    $success = $fixer->run();
    exit($success ? 0 : 1);
}
