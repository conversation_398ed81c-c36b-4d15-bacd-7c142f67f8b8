<?php

/**
 * 数据库问题修复工具
 * 解决部署过程中的数据库导入问题
 */

class DatabaseIssuesFixer
{
    private $config = [];
    
    public function __construct()
    {
        $this->loadEnvConfig();
    }
    
    private function loadEnvConfig()
    {
        if (!file_exists('.env')) {
            throw new Exception('.env文件不存在');
        }
        
        $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = explode('=', $line, 2);
                $this->config[trim($key)] = trim($value, '"\'');
            }
        }
    }
    
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    private function getDbConnection()
    {
        $host = $this->config['DB_HOST'] ?? '127.0.0.1';
        $port = $this->config['DB_PORT'] ?? '3306';
        $database = $this->config['DB_DATABASE'] ?? '';
        $username = $this->config['DB_USERNAME'] ?? '';
        $password = $this->config['DB_PASSWORD'] ?? '';
        
        $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
        
        try {
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            
            // 选择数据库
            $pdo->exec("USE `{$database}`");
            
            return $pdo;
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 完全重置数据库
     */
    public function resetDatabase()
    {
        $this->log('INFO', '开始完全重置数据库...');
        
        try {
            $pdo = $this->getDbConnection();
            
            // 禁用外键检查
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // 获取所有表
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                $this->log('INFO', '数据库中没有表，无需清理');
                return true;
            }
            
            $this->log('INFO', '发现 ' . count($tables) . ' 个表，开始删除...');
            
            // 删除所有表
            foreach ($tables as $table) {
                try {
                    $pdo->exec("DROP TABLE IF EXISTS `{$table}`");
                    $this->log('SUCCESS', "已删除表: {$table}");
                } catch (PDOException $e) {
                    $this->log('WARN', "删除表 {$table} 失败: " . $e->getMessage());
                }
            }
            
            // 重新启用外键检查
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            $this->log('SUCCESS', '数据库重置完成');
            return true;
            
        } catch (Exception $e) {
            $this->log('ERROR', '数据库重置失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 重新运行迁移
     */
    public function runMigrations()
    {
        $this->log('INFO', '开始运行数据库迁移...');
        
        // 检查artisan文件
        if (!file_exists('artisan')) {
            $this->log('ERROR', 'artisan文件不存在');
            return false;
        }
        
        // 运行迁移
        $commands = [
            'php artisan migrate:install --force',
            'php artisan migrate --force'
        ];
        
        foreach ($commands as $command) {
            $this->log('INFO', "执行命令: {$command}");
            
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->log('SUCCESS', "命令执行成功");
                foreach ($output as $line) {
                    if (trim($line)) {
                        $this->log('INFO', "  " . trim($line));
                    }
                }
            } else {
                $this->log('ERROR', "命令执行失败 (退出码: {$returnCode})");
                foreach ($output as $line) {
                    if (trim($line)) {
                        $this->log('ERROR', "  " . trim($line));
                    }
                }
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 运行数据填充
     */
    public function runSeeders()
    {
        $this->log('INFO', '开始运行数据填充...');
        
        if (!is_dir('database/seeders')) {
            $this->log('WARN', 'seeders目录不存在，跳过数据填充');
            return true;
        }
        
        $command = 'php artisan db:seed --force';
        $this->log('INFO', "执行命令: {$command}");
        
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            $this->log('SUCCESS', "数据填充成功");
            foreach ($output as $line) {
                if (trim($line)) {
                    $this->log('INFO', "  " . trim($line));
                }
            }
            return true;
        } else {
            $this->log('WARN', "数据填充失败，但这通常不影响系统运行");
            foreach ($output as $line) {
                if (trim($line)) {
                    $this->log('WARN', "  " . trim($line));
                }
            }
            return true; // 返回true，因为数据填充失败通常不是致命问题
        }
    }
    
    /**
     * 修复存储链接
     */
    public function fixStorageLink()
    {
        $this->log('INFO', '修复存储链接...');
        
        $linkPath = 'public/storage';
        $targetPath = '../storage/app/public';
        
        // 删除现有链接或目录
        if (file_exists($linkPath)) {
            if (is_link($linkPath)) {
                unlink($linkPath);
                $this->log('INFO', '已删除现有符号链接');
            } elseif (is_dir($linkPath)) {
                rmdir($linkPath);
                $this->log('INFO', '已删除现有目录');
            }
        }
        
        // 创建新的符号链接
        if (symlink($targetPath, $linkPath)) {
            $this->log('SUCCESS', '存储链接创建成功');
            return true;
        } else {
            $this->log('ERROR', '存储链接创建失败');
            return false;
        }
    }
    
    /**
     * 运行完整修复流程
     */
    public function runFullFix()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔧 数据库问题修复工具                             ║\n";
        echo "║                                                          ║\n";
        echo "║    解决部署过程中的数据库导入问题                         ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 步骤1: 重置数据库
        if (!$this->resetDatabase()) {
            $this->log('ERROR', '数据库重置失败，停止修复流程');
            return false;
        }
        
        // 步骤2: 运行迁移
        if (!$this->runMigrations()) {
            $this->log('ERROR', '数据库迁移失败，停止修复流程');
            return false;
        }
        
        // 步骤3: 运行数据填充
        $this->runSeeders();
        
        // 步骤4: 修复存储链接
        $this->fixStorageLink();
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        echo "\n";
        echo "============================================================\n";
        echo "                    修复完成\n";
        echo "============================================================\n";
        echo "\n";
        echo "🎉 数据库问题修复完成！\n";
        echo "⏱️  修复耗时: {$duration}秒\n";
        echo "\n";
        echo "✅ 现在可以正常使用系统了！\n";
        echo "\n";
        
        return true;
    }
}

// 运行修复工具
if (php_sapi_name() === 'cli') {
    try {
        $fixer = new DatabaseIssuesFixer();
        $success = $fixer->runFullFix();
        exit($success ? 0 : 1);
    } catch (Exception $e) {
        echo "\n❌ 修复工具运行失败: " . $e->getMessage() . "\n\n";
        exit(1);
    }
}
