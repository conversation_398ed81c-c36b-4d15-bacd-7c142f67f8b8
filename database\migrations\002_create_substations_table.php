<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 分站表迁移
 */
return new class extends Migration
{
    /**
     * 创建分站表
     */
    public function up()
    {
        // 检查表是否已存在，如果不存在才创建
        if (!Schema::hasTable('substations')) {
            Schema::create('substations', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('分站名称');
            $table->bigInteger('manager_id')->unsigned()->nullable()->comment('管理员用户ID');
            $table->string('domain')->nullable()->comment('分站域名');
            $table->tinyInteger('status')->default(1)->comment('状态：1-正常,0-禁用');
            $table->decimal('commission_rate', 5, 4)->default(0.1)->comment('佣金比例');
            $table->text('description')->nullable()->comment('分站描述');
            $table->string('contact_phone', 20)->nullable()->comment('联系电话');
            $table->string('contact_email', 100)->nullable()->comment('联系邮箱');
            $table->string('address')->nullable()->comment('地址');
            $table->json('settings')->nullable()->comment('分站设置');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['status']);
            $table->index(['manager_id']);
            $table->unique(['domain']);
            
            // 外键约束
            $table->foreign('manager_id')->references('id')->on('users')->onDelete('set null');
        });
        }
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('substations');
    }
};