<?php

/**
 * 简化版自动生成.env文件工具
 * 用于非交互式环境，通过命令行参数传递配置
 */

class SimpleEnvGenerator
{
    private $projectRoot;
    private $config = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    /**
     * 记录日志
     */
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$level}] {$timestamp} - {$message}\n";
    }
    
    /**
     * 生成随机密钥
     */
    private function generateAppKey()
    {
        return 'base64:' . base64_encode(random_bytes(32));
    }
    
    /**
     * 生成JWT密钥
     */
    private function generateJwtSecret()
    {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * 解析命令行参数
     */
    private function parseArguments($argv)
    {
        $config = [
            'domain' => '',
            'env' => 'production',
            'db_name' => '',
            'db_user' => '',
            'db_password' => '',
            'db_host' => '127.0.0.1',
            'db_port' => '3306'
        ];
        
        for ($i = 1; $i < count($argv); $i++) {
            $arg = $argv[$i];
            
            if (strpos($arg, '--') === 0) {
                $parts = explode('=', substr($arg, 2), 2);
                if (count($parts) === 2) {
                    $key = $parts[0];
                    $value = $parts[1];
                    
                    if (array_key_exists($key, $config)) {
                        $config[$key] = $value;
                    }
                }
            }
        }
        
        return $config;
    }
    
    /**
     * 生成完整的.env配置
     */
    private function generateConfig($params)
    {
        $this->config = [
            // 基础应用配置
            'APP_NAME' => '晨鑫流量变现系统',
            'APP_ENV' => $params['env'],
            'APP_KEY' => $this->generateAppKey(),
            'APP_DEBUG' => ($params['env'] === 'production') ? 'false' : 'true',
            'APP_URL' => 'https://' . $params['domain'],
            
            // 域名配置
            'API_DOMAIN' => $params['domain'],
            'FRONTEND_URL' => 'https://' . $params['domain'],
            'SESSION_DOMAIN' => '.' . $params['domain'],
            
            // 日志配置
            'LOG_CHANNEL' => 'daily',
            'LOG_LEVEL' => ($params['env'] === 'production') ? 'error' : 'debug',
            'LOG_DEPRECATIONS_CHANNEL' => 'null',
            
            // 数据库配置
            'DB_CONNECTION' => 'mysql',
            'DB_HOST' => $params['db_host'],
            'DB_PORT' => $params['db_port'],
            'DB_DATABASE' => $params['db_name'],
            'DB_USERNAME' => $params['db_user'],
            'DB_PASSWORD' => $params['db_password'],
            'DB_CHARSET' => 'utf8mb4',
            'DB_COLLATION' => 'utf8mb4_unicode_ci',
            'DB_STRICT' => 'true',
            'DB_ENGINE' => 'InnoDB',
            
            // 缓存和队列配置
            'BROADCAST_DRIVER' => 'log',
            'CACHE_DRIVER' => 'file',
            'FILESYSTEM_DRIVER' => 'local',
            'QUEUE_CONNECTION' => 'sync',
            'SESSION_DRIVER' => 'file',
            'SESSION_LIFETIME' => '120',
            
            // JWT配置
            'JWT_SECRET' => $this->generateJwtSecret(),
            'JWT_TTL' => '1440',
            'JWT_REFRESH_TTL' => '20160',
            'JWT_ALGO' => 'HS256',
            'JWT_BLACKLIST_ENABLED' => 'true',
            'JWT_BLACKLIST_GRACE_PERIOD' => '0',
            
            // 邮件配置
            'MAIL_MAILER' => 'log',
            'MAIL_HOST' => 'localhost',
            'MAIL_PORT' => '25',
            'MAIL_USERNAME' => '',
            'MAIL_PASSWORD' => '',
            'MAIL_ENCRYPTION' => 'null',
            'MAIL_FROM_ADDRESS' => 'noreply@' . $params['domain'],
            'MAIL_FROM_NAME' => '晨鑫流量变现系统',
            
            // 防红系统配置
            'ANTI_BLOCK_ENABLED' => 'true',
            'ANTI_BLOCK_CHECK_INTERVAL' => '300',
            'ANTI_BLOCK_DOMAIN_POOL_SIZE' => '10',
            'ANTI_BLOCK_FALLBACK_DOMAIN' => 'https://www.baidu.com',
            'ANTI_BLOCK_ALERTS_ENABLED' => 'true',
            'ANTI_BLOCK_WEBHOOK_URL' => '',
            'ANTI_BLOCK_NOTIFICATION_EMAIL' => '',
            'ANTI_BLOCK_IP_WHITELIST' => '',
            'ANTI_BLOCK_IP_BLACKLIST' => '',
            'ANTI_BLOCK_CDN_ENABLED' => 'false',
            'ANTI_BLOCK_CDN_PROVIDER' => 'cloudflare',
            
            // 支付配置
            'PAYMENT_ENABLED' => 'true',
            'PAYMENT_DEFAULT' => 'wechat',
            'PAYMENT_WECHAT_APPID' => '',
            'PAYMENT_WECHAT_MCHID' => '',
            'PAYMENT_WECHAT_KEY' => '',
            'WECHAT_PAY_APP_ID' => '',
            'WECHAT_PAY_MCH_ID' => '',
            'WECHAT_PAY_KEY' => '',
            'WECHAT_PAY_CERT_PATH' => '/www/wwwroot/' . $params['domain'] . '/storage/certs/wechat',
            'WECHAT_PAY_KEY_PATH' => '/www/wwwroot/' . $params['domain'] . '/storage/certs/wechat',
            'WECHAT_PAY_SANDBOX' => 'false',
            
            // 支付宝配置
            'PAYMENT_ALIPAY_APPID' => '',
            'PAYMENT_ALIPAY_KEY' => '',
            'ALIPAY_APP_ID' => '',
            'ALIPAY_PRIVATE_KEY' => '',
            'ALIPAY_PUBLIC_KEY' => '',
            'ALIPAY_SANDBOX' => 'false',
            
            // 短信配置
            'SMS_PROVIDER' => 'aliyun',
            'SMS_DRIVER' => 'aliyun',
            'SMS_ENABLED' => 'true',
            'SMS_TEST_MODE' => 'false',
            'SMS_ALIYUN_ACCESS_KEY_ID' => '',
            'SMS_ALIYUN_ACCESS_KEY_SECRET' => '',
            'SMS_ALIYUN_SIGN_NAME' => '晨鑫流量变现系统',
            'SMS_ALIYUN_TEMPLATE_CODE' => '',
            'SMS_ALIYUN_REGION' => 'cn-hangzhou',
            'SMS_ALIYUN_ENDPOINT' => 'dysmsapi.aliyuncs.com',
            
            // 短信发送配置
            'SMS_VERIFY_CODE_LENGTH' => '6',
            'SMS_VERIFY_CODE_EXPIRE' => '5',
            'SMS_SEND_INTERVAL' => '60',
            'SMS_DAILY_LIMIT' => '10',
            'SMS_IP_DAILY_LIMIT' => '100',
            'SMS_TIMEOUT' => '30',
            
            // 管理员配置
            'ADMIN_EMAIL' => 'admin@' . $params['domain'],
            'ALERT_WEBHOOK_URL' => '',
            'SERVICE_TOKEN' => '',
            
            // 系统监控配置
            'SYSTEM_MONITOR_ENABLED' => 'true',
            'SYSTEM_MONITOR_INTERVAL' => '300',
            'SYSTEM_MONITOR_ALERT_THRESHOLD' => '80',
            'HEALTH_CHECK_ENABLED' => 'true',
            'HEALTH_CHECK_INTERVAL' => '60',
            'HEALTH_SCORE_THRESHOLD' => '70',
            
            // IP地理位置服务配置
            'IP_LOCATION_CACHE_ENABLED' => 'true',
            'IP_LOCATION_CACHE_TTL' => '3600',
            'BAIDU_MAP_ENABLED' => 'true',
            'BAIDU_MAP_API_KEY' => 'VHr3iKqzTkSq4fBgK7ITGAyQ8FFZR0Om',
            'BAIDU_MAP_TIMEOUT' => '5',
            'BAIDU_CLOUD_ENABLED' => 'true',
            'BAIDU_CLOUD_TIMEOUT' => '5',
            'IP_API_ENABLED' => 'true',
            'IP_API_TIMEOUT' => '5',
            'DEFAULT_CITY' => '本地',
            
            // 群组营销功能配置
            'GROUP_MARKETING_ENABLED' => 'true',
            'VIRTUAL_DATA_ENABLED' => 'true',
            'CITY_REPLACE_ENABLED' => 'true',
            'BROWSER_DETECTION_ENABLED' => 'true',
            'ACCESS_LOG_ENABLED' => 'true',
            'ACCESS_LOG_RETENTION_DAYS' => '30',
            
            // 文件上传配置
            'UPLOAD_MAX_SIZE' => '10240',
            'UPLOAD_ALLOWED_TYPES' => 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx',
            'UPLOAD_PATH' => 'uploads',
            'FILESYSTEM_DISK' => 'local',
            
            // 操作日志配置
            'OPERATION_LOG_ENABLED' => 'true',
            'OPERATION_LOG_RETENTION_DAYS' => '90',
            
            // 宝塔环境特殊配置
            'BAOTA_ENABLED' => 'true',
            'PAYMENT_CERT_DIR' => '/www/wwwroot/' . $params['domain'] . '/storage/certs',
            'PAYMENT_LOG_DIR' => '/www/wwwroot/' . $params['domain'] . '/storage/logs/payment',
            'PAYMENT_BACKUP_DIR' => '/www/backup/payment',
            
            // 队列和任务配置
            'QUEUE_FAILED_DRIVER' => 'database-uuids',
            'HORIZON_ENABLED' => 'false',
            
            // 缓存配置优化
            'CACHE_PREFIX' => 'ffjq_',
            'CACHE_TTL' => '3600',
            
            // 安全配置
            'SECURITY_RATE_LIMIT_ENABLED' => 'true',
            'SECURITY_MAX_LOGIN_ATTEMPTS' => '5',
            'SECURITY_LOGIN_LOCKOUT_TIME' => '300',
            'SECURITY_PASSWORD_MIN_LENGTH' => '6',
            
            // 调试和开发工具
            'TELESCOPE_ENABLED' => ($params['env'] === 'production') ? 'false' : 'true',
            'DEBUGBAR_ENABLED' => ($params['env'] === 'production') ? 'false' : 'true',
            'TESTING_MODE' => 'false',
            
            // 多语言配置
            'APP_LOCALE' => 'zh_CN',
            'APP_FALLBACK_LOCALE' => 'zh_CN',
            'APP_TIMEZONE' => 'Asia/Shanghai',
            
            // API配置
            'API_RATE_LIMIT' => '60',
            'API_THROTTLE_ENABLED' => 'true',
            'API_VERSION' => 'v1',
            
            // 数据统计配置
            'ANALYTICS_ENABLED' => 'true',
            'ANALYTICS_RETENTION_DAYS' => '365',
            
            // 备份配置
            'BACKUP_ENABLED' => 'true',
            'BACKUP_RETENTION_DAYS' => '30',
            'BACKUP_DISK' => 'local',
            
            // 性能优化配置
            'OPCACHE_ENABLED' => 'true',
            'QUERY_LOG_ENABLED' => 'false',
            'SLOW_QUERY_LOG_ENABLED' => 'true',
            'SLOW_QUERY_TIME' => '2'
        ];
    }
    
    /**
     * 生成.env文件内容
     */
    private function generateEnvContent()
    {
        $content = "# 自动生成的环境配置文件\n";
        $content .= "# 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $content .= "# 请勿手动修改，如需更改请重新运行生成工具\n\n";
        
        foreach ($this->config as $key => $value) {
            // 如果值包含空格或特殊字符，用引号包围
            if (strpos($value, ' ') !== false || strpos($value, '#') !== false) {
                $value = '"' . $value . '"';
            }
            $content .= "{$key}={$value}\n";
        }
        
        return $content;
    }
    
    /**
     * 保存.env文件
     */
    private function saveEnvFile()
    {
        $envFile = $this->projectRoot . '/.env';
        $content = $this->generateEnvContent();
        
        // 备份现有.env文件
        if (file_exists($envFile)) {
            $backupFile = $envFile . '.backup.' . date('YmdHis');
            copy($envFile, $backupFile);
            $this->log('INFO', "已备份现有.env文件到: " . basename($backupFile));
        }
        
        // 写入新的.env文件
        if (file_put_contents($envFile, $content)) {
            $this->log('SUCCESS', '.env文件生成成功');
            return true;
        } else {
            $this->log('ERROR', '.env文件生成失败');
            return false;
        }
    }
    
    /**
     * 运行生成流程
     */
    public function run($argv)
    {
        $params = $this->parseArguments($argv);
        
        // 验证必需参数
        if (empty($params['domain']) || empty($params['db_name']) || empty($params['db_user'])) {
            echo "用法: php auto_generate_env_simple.php --domain=example.com --db_name=database --db_user=user [--db_password=pass] [--env=production]\n";
            return false;
        }
        
        $this->log('INFO', '开始生成.env文件...');
        $this->log('INFO', '域名: ' . $params['domain']);
        $this->log('INFO', '环境: ' . $params['env']);
        $this->log('INFO', '数据库: ' . $params['db_name']);
        
        $this->generateConfig($params);
        
        if ($this->saveEnvFile()) {
            $this->log('SUCCESS', '.env文件生成完成，包含 ' . count($this->config) . ' 个配置项');
            return true;
        } else {
            return false;
        }
    }
}

// 运行简化生成工具
if (php_sapi_name() === 'cli') {
    $generator = new SimpleEnvGenerator();
    $success = $generator->run($argv);
    exit($success ? 0 : 1);
}
