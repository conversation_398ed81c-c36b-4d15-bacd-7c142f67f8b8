<?php

/**
 * 前端问题诊断和修复工具
 * 专门解决管理后台页面空白和JavaScript错误问题
 */

class FrontendIssuesFixer
{
    private $projectRoot;
    private $fixes = [];
    private $errors = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'FIX' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 检查前端项目结构
     */
    private function checkFrontendStructure()
    {
        $this->log('INFO', '检查前端项目结构...');
        
        $frontendDirs = [
            'admin' => '管理后台',
            'frontend' => '前端应用',
            'distributor' => '分销后台'
        ];
        
        $foundDirs = [];
        foreach ($frontendDirs as $dir => $description) {
            $path = $this->projectRoot . '/' . $dir;
            if (is_dir($path)) {
                $foundDirs[] = $dir;
                $this->log('SUCCESS', "发现{$description}: {$dir}/");
                
                // 检查关键文件
                $keyFiles = ['index.html', 'package.json', 'dist/'];
                foreach ($keyFiles as $file) {
                    $filePath = $path . '/' . $file;
                    if (file_exists($filePath) || is_dir($filePath)) {
                        $this->log('SUCCESS', "  ✅ {$file}");
                    } else {
                        $this->log('WARN', "  ❌ {$file} 缺失");
                    }
                }
            }
        }
        
        if (empty($foundDirs)) {
            $this->log('ERROR', '未发现前端项目目录');
            $this->errors[] = '前端项目目录缺失';
            return false;
        }
        
        return $foundDirs;
    }
    
    /**
     * 检查Laravel前端资源配置
     */
    private function checkLaravelFrontendConfig()
    {
        $this->log('INFO', '检查Laravel前端资源配置...');
        
        // 检查public目录下的前端资源
        $publicDirs = ['admin', 'frontend', 'distributor'];
        $missingDirs = [];
        
        foreach ($publicDirs as $dir) {
            $path = $this->projectRoot . '/public/' . $dir;
            if (is_dir($path)) {
                $this->log('SUCCESS', "发现public/{$dir}/目录");
                
                // 检查关键文件
                $indexFile = $path . '/index.html';
                if (file_exists($indexFile)) {
                    $this->log('SUCCESS', "  ✅ index.html存在");
                } else {
                    $this->log('WARN', "  ❌ index.html缺失");
                    $missingDirs[] = $dir;
                }
            } else {
                $this->log('WARN', "public/{$dir}/目录不存在");
                $missingDirs[] = $dir;
            }
        }
        
        return $missingDirs;
    }
    
    /**
     * 修复前端资源链接
     */
    private function fixFrontendLinks($missingDirs)
    {
        if (empty($missingDirs)) {
            $this->log('SUCCESS', '前端资源链接正常');
            return true;
        }
        
        $this->log('FIX', '修复前端资源链接...');
        
        foreach ($missingDirs as $dir) {
            $sourcePath = $this->projectRoot . '/' . $dir . '/dist';
            $targetPath = $this->projectRoot . '/public/' . $dir;
            
            if (is_dir($sourcePath)) {
                // 创建符号链接
                if (symlink($sourcePath, $targetPath)) {
                    $this->log('SUCCESS', "创建符号链接: public/{$dir} -> {$dir}/dist");
                    $this->fixes[] = "创建前端资源链接: {$dir}";
                } else {
                    // 如果符号链接失败，尝试复制
                    if ($this->copyDirectory($sourcePath, $targetPath)) {
                        $this->log('SUCCESS', "复制前端资源: {$dir}/dist -> public/{$dir}");
                        $this->fixes[] = "复制前端资源: {$dir}";
                    } else {
                        $this->log('ERROR', "无法创建前端资源链接: {$dir}");
                        $this->errors[] = "前端资源链接失败: {$dir}";
                    }
                }
            } else {
                $this->log('WARN', "源目录不存在: {$sourcePath}");
                
                // 尝试构建前端项目
                $this->buildFrontendProject($dir);
            }
        }
        
        return empty($this->errors);
    }
    
    /**
     * 构建前端项目
     */
    private function buildFrontendProject($dir)
    {
        $this->log('FIX', "尝试构建前端项目: {$dir}");
        
        $projectPath = $this->projectRoot . '/' . $dir;
        if (!is_dir($projectPath)) {
            $this->log('ERROR', "前端项目目录不存在: {$dir}");
            return false;
        }
        
        // 检查package.json
        $packageJson = $projectPath . '/package.json';
        if (!file_exists($packageJson)) {
            $this->log('ERROR', "package.json不存在: {$dir}");
            return false;
        }
        
        // 切换到项目目录并构建
        $oldCwd = getcwd();
        chdir($projectPath);
        
        $this->log('INFO', "在{$dir}目录中执行npm构建...");
        
        // 检查node_modules
        if (!is_dir('node_modules')) {
            $this->log('INFO', '安装npm依赖...');
            $output = [];
            $returnCode = 0;
            exec('npm install 2>&1', $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->log('WARN', 'npm install失败，尝试使用yarn...');
                exec('yarn install 2>&1', $output, $returnCode);
                
                if ($returnCode !== 0) {
                    $this->log('ERROR', '依赖安装失败');
                    chdir($oldCwd);
                    return false;
                }
            }
        }
        
        // 构建项目
        $this->log('INFO', '构建前端项目...');
        $output = [];
        $returnCode = 0;
        exec('npm run build 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            $this->log('SUCCESS', "前端项目构建成功: {$dir}");
            $this->fixes[] = "构建前端项目: {$dir}";
            chdir($oldCwd);
            return true;
        } else {
            $this->log('WARN', 'npm run build失败，尝试其他构建命令...');
            
            // 尝试其他可能的构建命令
            $buildCommands = ['npm run prod', 'yarn build', 'yarn prod'];
            foreach ($buildCommands as $command) {
                $output = [];
                $returnCode = 0;
                exec($command . ' 2>&1', $output, $returnCode);
                
                if ($returnCode === 0) {
                    $this->log('SUCCESS', "前端项目构建成功: {$dir} (使用 {$command})");
                    $this->fixes[] = "构建前端项目: {$dir}";
                    chdir($oldCwd);
                    return true;
                }
            }
            
            $this->log('ERROR', "前端项目构建失败: {$dir}");
            $this->errors[] = "前端构建失败: {$dir}";
            chdir($oldCwd);
            return false;
        }
    }
    
    /**
     * 复制目录
     */
    private function copyDirectory($source, $destination)
    {
        if (!is_dir($source)) {
            return false;
        }
        
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $target = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($target)) {
                    mkdir($target, 0755, true);
                }
            } else {
                copy($item, $target);
            }
        }
        
        return true;
    }
    
    /**
     * 清理Laravel缓存
     */
    private function clearLaravelCache()
    {
        $this->log('FIX', '清理Laravel缓存...');
        
        $commands = [
            'php artisan config:clear' => '清除配置缓存',
            'php artisan route:clear' => '清除路由缓存',
            'php artisan view:clear' => '清除视图缓存',
            'php artisan cache:clear' => '清除应用缓存',
            'php artisan optimize:clear' => '清除优化缓存'
        ];
        
        foreach ($commands as $command => $description) {
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->log('SUCCESS', $description . '成功');
                $this->fixes[] = $description;
            } else {
                $this->log('WARN', $description . '失败');
            }
        }
    }
    
    /**
     * 检查Web服务器配置
     */
    private function checkWebServerConfig()
    {
        $this->log('INFO', '检查Web服务器配置...');
        
        // 检查.htaccess文件
        $htaccessFile = $this->projectRoot . '/public/.htaccess';
        if (file_exists($htaccessFile)) {
            $this->log('SUCCESS', '.htaccess文件存在');
        } else {
            $this->log('WARN', '.htaccess文件不存在，可能影响路由');
        }
        
        // 检查index.php
        $indexFile = $this->projectRoot . '/public/index.php';
        if (file_exists($indexFile)) {
            $this->log('SUCCESS', 'public/index.php存在');
        } else {
            $this->log('ERROR', 'public/index.php不存在');
            $this->errors[] = 'Laravel入口文件缺失';
        }
        
        // 检查存储链接
        $storageLink = $this->projectRoot . '/public/storage';
        if (is_link($storageLink)) {
            $this->log('SUCCESS', '存储链接存在');
        } else {
            $this->log('WARN', '存储链接不存在，创建链接...');
            $output = [];
            $returnCode = 0;
            exec('php artisan storage:link 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->log('SUCCESS', '存储链接创建成功');
                $this->fixes[] = '创建存储链接';
            } else {
                $this->log('WARN', '存储链接创建失败');
            }
        }
    }
    
    /**
     * 运行修复流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🎨 前端问题诊断和修复工具                         ║\n";
        echo "║                                                          ║\n";
        echo "║    解决管理后台页面空白和JavaScript错误问题               ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 执行诊断和修复
        $frontendDirs = $this->checkFrontendStructure();
        $missingDirs = $this->checkLaravelFrontendConfig();
        
        if ($frontendDirs !== false) {
            $this->fixFrontendLinks($missingDirs);
        }
        
        $this->clearLaravelCache();
        $this->checkWebServerConfig();
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 显示修复结果
        echo "\n";
        echo "============================================================\n";
        echo "                    修复结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if (empty($this->errors)) {
            echo "🎉 前端问题修复完成！\n";
        } else {
            echo "⚠️  修复完成，但存在 " . count($this->errors) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 修复统计:\n";
        echo "   修复项目: " . count($this->fixes) . " 个\n";
        echo "   发现问题: " . count($this->errors) . " 个\n";
        echo "   修复耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->errors as $error) {
                echo "   - {$error}\n";
            }
            echo "\n";
        }
        
        echo "🚀 下一步操作:\n";
        if (empty($this->errors)) {
            echo "   1. 刷新浏览器页面\n";
            echo "   2. 清除浏览器缓存\n";
            echo "   3. 访问管理后台: https://f.fcwan.cn/admin\n";
            echo "   4. 如果仍有问题，检查浏览器控制台错误\n";
        } else {
            echo "   1. 解决上述问题\n";
            echo "   2. 重新运行修复工具\n";
            echo "   3. 检查前端项目构建状态\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 如果页面仍然空白，可能需要手动构建前端项目。\n";
        echo "\n";
        
        return empty($this->errors);
    }
}

// 运行修复工具
if (php_sapi_name() === 'cli') {
    $fixer = new FrontendIssuesFixer();
    $success = $fixer->run();
    exit($success ? 0 : 1);
}
