<?php

/**
 * 修复当前部署问题的快速脚本
 * 专门解决f.fcwan.cn部署中遇到的具体问题
 */

class CurrentDeploymentFixer
{
    private $projectRoot;
    private $fixes = [];
    private $errors = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'FIX' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 修复.env文件配置
     */
    private function fixEnvFile()
    {
        $this->log('FIX', '修复.env文件配置...');
        
        $envFile = $this->projectRoot . '/.env';
        if (!file_exists($envFile)) {
            $this->log('ERROR', '.env文件不存在');
            return false;
        }
        
        // 读取现有配置
        $content = file_get_contents($envFile);
        $lines = explode("\n", $content);
        $config = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $config[trim($key)] = trim($value, '"\'');
            }
        }
        
        // 修复配置
        $needsFix = false;
        
        // 修复域名配置
        if (!isset($config['APP_URL']) || $config['APP_URL'] !== 'https://f.fcwan.cn') {
            $config['APP_URL'] = 'https://f.fcwan.cn';
            $needsFix = true;
            $this->fixes[] = '修复APP_URL为https://f.fcwan.cn';
        }
        
        if (!isset($config['API_DOMAIN']) || $config['API_DOMAIN'] !== 'f.fcwan.cn') {
            $config['API_DOMAIN'] = 'f.fcwan.cn';
            $needsFix = true;
            $this->fixes[] = '修复API_DOMAIN为f.fcwan.cn';
        }
        
        if (!isset($config['FRONTEND_URL']) || $config['FRONTEND_URL'] !== 'https://f.fcwan.cn') {
            $config['FRONTEND_URL'] = 'https://f.fcwan.cn';
            $needsFix = true;
            $this->fixes[] = '修复FRONTEND_URL为https://f.fcwan.cn';
        }
        
        if (!isset($config['SESSION_DOMAIN']) || $config['SESSION_DOMAIN'] !== '.f.fcwan.cn') {
            $config['SESSION_DOMAIN'] = '.f.fcwan.cn';
            $needsFix = true;
            $this->fixes[] = '修复SESSION_DOMAIN为.f.fcwan.cn';
        }
        
        // 修复数据库配置
        if (!isset($config['DB_HOST']) || $config['DB_HOST'] !== '127.0.0.1') {
            $config['DB_HOST'] = '127.0.0.1';
            $needsFix = true;
            $this->fixes[] = '修复DB_HOST为127.0.0.1';
        }
        
        if (!isset($config['DB_DATABASE']) || $config['DB_DATABASE'] !== 'ffjq') {
            $config['DB_DATABASE'] = 'ffjq';
            $needsFix = true;
            $this->fixes[] = '修复DB_DATABASE为ffjq';
        }
        
        if (!isset($config['DB_USERNAME']) || $config['DB_USERNAME'] !== 'ffjq') {
            $config['DB_USERNAME'] = 'ffjq';
            $needsFix = true;
            $this->fixes[] = '修复DB_USERNAME为ffjq';
        }
        
        // 确保必需的密钥存在
        if (empty($config['APP_KEY'])) {
            $config['APP_KEY'] = 'base64:' . base64_encode(random_bytes(32));
            $needsFix = true;
            $this->fixes[] = '生成新的APP_KEY';
        }
        
        if (empty($config['JWT_SECRET'])) {
            $config['JWT_SECRET'] = bin2hex(random_bytes(32));
            $needsFix = true;
            $this->fixes[] = '生成新的JWT_SECRET';
        }
        
        // 如果需要修复，写入文件
        if ($needsFix) {
            // 备份原文件
            $backupFile = $envFile . '.backup.' . date('YmdHis');
            copy($envFile, $backupFile);
            $this->log('INFO', "已备份.env文件到: " . basename($backupFile));
            
            // 生成新内容
            $newContent = "# 修复后的环境配置文件\n";
            $newContent .= "# 修复时间: " . date('Y-m-d H:i:s') . "\n\n";
            
            foreach ($config as $key => $value) {
                if (strpos($value, ' ') !== false || strpos($value, '#') !== false) {
                    $value = '"' . $value . '"';
                }
                $newContent .= "{$key}={$value}\n";
            }
            
            if (file_put_contents($envFile, $newContent)) {
                $this->log('SUCCESS', '.env文件修复成功');
                return true;
            } else {
                $this->log('ERROR', '.env文件修复失败');
                return false;
            }
        } else {
            $this->log('SUCCESS', '.env文件配置正确，无需修复');
            return true;
        }
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection()
    {
        $this->log('INFO', '测试数据库连接...');
        
        // 读取.env配置
        $envFile = $this->projectRoot . '/.env';
        if (!file_exists($envFile)) {
            $this->log('ERROR', '.env文件不存在');
            return false;
        }
        
        $content = file_get_contents($envFile);
        $lines = explode("\n", $content);
        $config = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $config[trim($key)] = trim($value, '"\'');
            }
        }
        
        $host = $config['DB_HOST'] ?? '127.0.0.1';
        $port = $config['DB_PORT'] ?? '3306';
        $database = $config['DB_DATABASE'] ?? '';
        $username = $config['DB_USERNAME'] ?? '';
        $password = $config['DB_PASSWORD'] ?? '';
        
        if (empty($database) || empty($username)) {
            $this->log('ERROR', '数据库配置不完整');
            return false;
        }
        
        try {
            $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5,
            ]);
            
            // 测试数据库是否存在
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$database]);
            
            if ($stmt->rowCount() > 0) {
                $this->log('SUCCESS', '数据库连接测试成功');
                return true;
            } else {
                $this->log('WARN', '数据库连接成功，但指定的数据库不存在');
                $this->errors[] = "数据库 '{$database}' 不存在，请先创建";
                return false;
            }
        } catch (PDOException $e) {
            $this->log('ERROR', '数据库连接失败: ' . $e->getMessage());
            $this->errors[] = '数据库连接失败: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 运行Laravel命令
     */
    private function runLaravelCommands()
    {
        $this->log('INFO', '运行Laravel配置命令...');
        
        $commands = [
            'php artisan key:generate --force' => '生成应用密钥',
            'php artisan jwt:secret --force' => '生成JWT密钥',
            'php artisan config:clear' => '清除配置缓存',
            'php artisan route:clear' => '清除路由缓存',
            'php artisan view:clear' => '清除视图缓存',
            'php artisan cache:clear' => '清除应用缓存',
            'php artisan storage:link' => '创建存储链接'
        ];
        
        foreach ($commands as $command => $description) {
            $this->log('INFO', "执行: {$description}");
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->log('SUCCESS', "{$description}成功");
                $this->fixes[] = $description;
            } else {
                $this->log('WARN', "{$description}失败: " . implode(' ', $output));
            }
        }
    }
    
    /**
     * 运行修复流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔧 当前部署问题快速修复工具                       ║\n";
        echo "║                                                          ║\n";
        echo "║    专门修复f.fcwan.cn部署中的配置问题                     ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 执行修复步骤
        $envFixed = $this->fixEnvFile();
        $dbConnected = $this->testDatabaseConnection();
        $this->runLaravelCommands();
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 显示修复结果
        echo "\n";
        echo "============================================================\n";
        echo "                    修复结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if (empty($this->errors)) {
            echo "🎉 修复完成！\n";
        } else {
            echo "⚠️  修复完成，但存在 " . count($this->errors) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 修复统计:\n";
        echo "   修复项目: " . count($this->fixes) . " 个\n";
        echo "   发现问题: " . count($this->errors) . " 个\n";
        echo "   .env文件: " . ($envFixed ? "✅ 已修复" : "❌ 修复失败") . "\n";
        echo "   数据库连接: " . ($dbConnected ? "✅ 正常" : "❌ 失败") . "\n";
        echo "   修复耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->errors as $error) {
                echo "   - {$error}\n";
            }
            echo "\n";
        }
        
        echo "🚀 下一步操作:\n";
        if ($dbConnected) {
            echo "   1. 运行数据库迁移: php artisan migrate --force\n";
            echo "   2. 填充初始数据: php artisan db:seed --force\n";
            echo "   3. 访问网站验证: https://f.fcwan.cn\n";
        } else {
            echo "   1. 检查数据库是否已创建\n";
            echo "   2. 确认数据库密码设置\n";
            echo "   3. 重新运行修复工具\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 修复完成后可以继续运行部署脚本。\n";
        echo "\n";
        
        return empty($this->errors);
    }
}

// 运行修复工具
if (php_sapi_name() === 'cli') {
    $fixer = new CurrentDeploymentFixer();
    $success = $fixer->run();
    exit($success ? 0 : 1);
}
