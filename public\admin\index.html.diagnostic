<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>晨鑫流量变现系统 - 诊断模式</title>
  <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">
  
  <script>
    // 诊断模式 - 检查资源加载情况
    let diagnosticInfo = {
      cssLoaded: false,
      jsLoaded: false,
      errors: [],
      startTime: Date.now()
    };
    
    // 错误收集
    window.onerror = function(msg, url, line, col, error) {
      diagnosticInfo.errors.push({
        type: 'JavaScript Error',
        message: msg,
        url: url,
        line: line,
        error: error ? error.toString() : 'Unknown'
      });
      
      // 如果是xt错误，不阻止执行
      if (msg && msg.includes('xt') && msg.includes('Cannot access')) {
        return true;
      }
      return false;
    };
    
    window.addEventListener('unhandledrejection', function(event) {
      diagnosticInfo.errors.push({
        type: 'Promise Rejection',
        message: event.reason ? event.reason.toString() : 'Unknown promise rejection'
      });
      
      if (event.reason && event.reason.message && event.reason.message.includes('xt')) {
        event.preventDefault();
      }
    });
    
    // 检查CSS加载
    document.addEventListener('DOMContentLoaded', function() {
      const links = document.querySelectorAll('link[rel="stylesheet"]');
      let loadedCount = 0;
      
      links.forEach(function(link) {
        link.onload = function() {
          loadedCount++;
          if (loadedCount === links.length) {
            diagnosticInfo.cssLoaded = true;
          }
        };
        
        link.onerror = function() {
          diagnosticInfo.errors.push({
            type: 'CSS Load Error',
            url: link.href
          });
        };
      });
      
      // 5秒后开始诊断
      setTimeout(showDiagnostic, 5000);
    });
    
    function showDiagnostic() {
      const app = document.getElementById('app');
      const elapsed = Date.now() - diagnosticInfo.startTime;
      
      // 检查Vue应用是否启动
      const hasVueApp = app && (
        app.querySelector('[class*="el-"]') ||
        app.querySelector('[data-v-]') ||
        app.querySelector('.router-view') ||
        (app.children.length > 1 && !app.innerHTML.includes('诊断模式'))
      );
      
      if (hasVueApp) {
        console.log('✅ Vue应用启动成功');
        return;
      }
      
      // 显示诊断信息
      app.innerHTML = `
        <div style="
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: #f5f5f5;
          min-height: 100vh;
        ">
          <div style="
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          ">
            <h1 style="color: #e74c3c; margin-bottom: 20px;">🔍 前端诊断报告</h1>
            
            <div style="margin-bottom: 20px;">
              <h3>📊 基本信息</h3>
              <ul style="line-height: 1.6;">
                <li><strong>启动时间:</strong> ${elapsed}ms</li>
                <li><strong>CSS加载:</strong> ${diagnosticInfo.cssLoaded ? '✅ 成功' : '❌ 失败'}</li>
                <li><strong>用户代理:</strong> ${navigator.userAgent}</li>
                <li><strong>页面URL:</strong> ${window.location.href}</li>
              </ul>
            </div>
            
            <div style="margin-bottom: 20px;">
              <h3>🔗 资源检查</h3>
              <div id="resource-check">正在检查资源...</div>
            </div>
            
            <div style="margin-bottom: 20px;">
              <h3>❌ 错误日志 (${diagnosticInfo.errors.length})</h3>
              <div style="
                background: #f8f9fa;
                border-radius: 6px;
                padding: 15px;
                max-height: 300px;
                overflow-y: auto;
                font-family: monospace;
                font-size: 12px;
              ">
                ${diagnosticInfo.errors.length === 0 ? 
                  '<div style="color: #28a745;">没有发现错误</div>' :
                  diagnosticInfo.errors.map(err => 
                    '<div style="margin-bottom: 10px; padding: 8px; background: #fff; border-left: 3px solid #dc3545;">' +
                    '<strong>' + err.type + ':</strong><br>' +
                    err.message + (err.url ? '<br><small>URL: ' + err.url + '</small>' : '') +
                    '</div>'
                  ).join('')
                }
              </div>
            </div>
            
            <div style="margin-bottom: 20px;">
              <h3>🛠️ 解决方案</h3>
              <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button onclick="tryReload()" style="
                  background: #007bff;
                  color: white;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 6px;
                  cursor: pointer;
                ">🔄 重新加载</button>
                
                <button onclick="clearCache()" style="
                  background: #dc3545;
                  color: white;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 6px;
                  cursor: pointer;
                ">🧹 清除缓存</button>
                
                <button onclick="useSimpleMode()" style="
                  background: #28a745;
                  color: white;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 6px;
                  cursor: pointer;
                ">🔧 简单模式</button>
                
                <button onclick="checkResources()" style="
                  background: #ffc107;
                  color: #212529;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 6px;
                  cursor: pointer;
                ">🔍 检查资源</button>
              </div>
            </div>
            
            <div style="
              background: #e9ecef;
              padding: 15px;
              border-radius: 6px;
              font-size: 14px;
              color: #6c757d;
            ">
              <strong>💡 建议:</strong> 如果错误日志中有很多 "Cannot access 'xt'" 错误，
              这是已知的Vue/Element Plus兼容性问题。建议使用简单模式或清除缓存后重试。
            </div>
          </div>
        </div>
        
        <script>
          function tryReload() {
            window.location.reload(true);
          }
          
          function clearCache() {
            sessionStorage.clear();
            localStorage.clear();
            if ('caches' in window) {
              caches.keys().then(names => {
                return Promise.all(names.map(name => caches.delete(name)));
              }).then(() => {
                window.location.reload(true);
              });
            } else {
              window.location.reload(true);
            }
          }
          
          function useSimpleMode() {
            sessionStorage.setItem('force_simple_mode', 'true');
            window.location.href = window.location.href.split('?')[0] + '?mode=simple';
          }
          
          function checkResources() {
            const resourceCheck = document.getElementById('resource-check');
            resourceCheck.innerHTML = '正在检查...';
            
            const resources = [
              '/admin/assets/index-XVEiIDg_.css',
              '/admin/assets/index-BWOuGn0N.js',
              '/admin/assets/vue-vendor-B18IO7kr.js',
              '/admin/assets/element-plus-BNplCcqW.js'
            ];
            
            let results = [];
            let completed = 0;
            
            resources.forEach(function(url) {
              fetch(url, { method: 'HEAD' })
                .then(function(response) {
                  results.push({
                    url: url,
                    status: response.status,
                    ok: response.ok
                  });
                })
                .catch(function(error) {
                  results.push({
                    url: url,
                    status: 'Error',
                    ok: false,
                    error: error.message
                  });
                })
                .finally(function() {
                  completed++;
                  if (completed === resources.length) {
                    displayResourceResults(results);
                  }
                });
            });
          }
          
          function displayResourceResults(results) {
            const resourceCheck = document.getElementById('resource-check');
            resourceCheck.innerHTML = results.map(function(result) {
              const status = result.ok ? '✅' : '❌';
              return '<div style="margin: 5px 0; font-family: monospace; font-size: 12px;">' +
                     status + ' ' + result.url + ' (' + result.status + ')' +
                     (result.error ? ' - ' + result.error : '') +
                     '</div>';
            }).join('');
          }
        </script>
      `;
      
      // 自动检查资源
      setTimeout(function() {
        if (typeof checkResources === 'function') {
          checkResources();
        }
      }, 1000);
    }
  </script>
</head>
<body>
  <div id="app">
    <div style="
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      ">
        <div style="
          width: 50px;
          height: 50px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #409EFF;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 20px;
        "></div>
        <h3 style="color: #333; margin-bottom: 10px;">诊断模式</h3>
        <p style="color: #666;">正在检查前端资源...</p>
      </div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      body { margin: 0; padding: 0; }
    </style>
  </div>
  
  <!-- 延迟加载主应用进行测试 -->
  <script>
    setTimeout(function() {
      const script = document.createElement('script');
      script.type = 'module';
      script.crossOrigin = 'anonymous';
      script.src = '/admin/assets/index-BWOuGn0N.js';
      script.onload = function() {
        diagnosticInfo.jsLoaded = true;
      };
      script.onerror = function() {
        diagnosticInfo.errors.push({
          type: 'Main Script Load Error',
          url: '/admin/assets/index-BWOuGn0N.js'
        });
      };
      document.head.appendChild(script);
    }, 1000);
  </script>
</body>
</html>
