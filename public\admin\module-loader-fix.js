/**
 * Vue模块加载修复脚本
 * 专门解决 'Cannot access before initialization' 错误
 */

(function() {
    'use strict';
    
    console.log('🔧 模块加载修复脚本启动');
    
    // 错误计数和时间管理
    const ERROR_STORAGE_KEY = 'vue_module_load_errors';
    const MAX_RETRIES = 2;
    const RETRY_DELAY = 1000;
    const ERROR_RESET_TIME = 300000; // 5分钟后重置错误计数
    
    // 获取或初始化错误数据
    function getErrorData() {
        const stored = sessionStorage.getItem(ERROR_STORAGE_KEY);
        const now = Date.now();
        
        if (stored) {
            const data = JSON.parse(stored);
            // 如果超过重置时间，清零计数
            if (now - data.timestamp > ERROR_RESET_TIME) {
                return { count: 0, timestamp: now };
            }
            return data;
        }
        
        return { count: 0, timestamp: now };
    }
    
    // 保存错误数据
    function saveErrorData(data) {
        sessionStorage.setItem(ERROR_STORAGE_KEY, JSON.stringify(data));
    }
    
    // 清除错误数据
    function clearErrorData() {
        sessionStorage.removeItem(ERROR_STORAGE_KEY);
    }
    
    // 检查是否是模块初始化错误
    function isModuleInitError(error) {
        if (!error || !error.message) return false;
        
        const message = error.message.toLowerCase();
        return (
            message.includes('cannot access') && 
            message.includes('before initialization')
        ) || (
            message.includes('xt') && 
            (message.includes('not defined') || message.includes('initialization'))
        );
    }
    
    // 显示友好的错误页面
    function showErrorPage() {
        const app = document.getElementById('app');
        if (!app) return;
        
        app.innerHTML = `
            <div style="
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                padding: 20px;
                box-sizing: border-box;
            ">
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 16px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
                    text-align: center;
                    max-width: 480px;
                    width: 100%;
                ">
                    <div style="
                        width: 80px;
                        height: 80px;
                        background: #f56565;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 24px;
                        font-size: 36px;
                    ">⚠️</div>
                    
                    <h2 style="
                        color: #2d3748;
                        margin: 0 0 16px 0;
                        font-size: 24px;
                        font-weight: 600;
                    ">模块加载失败</h2>
                    
                    <p style="
                        color: #718096;
                        margin: 0 0 32px 0;
                        line-height: 1.6;
                        font-size: 16px;
                    ">前端资源加载出现问题，这通常是由于网络问题或浏览器缓存导致的。</p>
                    
                    <div style="margin-bottom: 24px;">
                        <button onclick="window.location.reload(true)" style="
                            background: #4299e1;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: 500;
                            margin: 0 8px 8px 0;
                            transition: all 0.2s;
                        " onmouseover="this.style.background='#3182ce'" onmouseout="this.style.background='#4299e1'">
                            🔄 重新加载
                        </button>
                        
                        <button onclick="clearCacheAndReload()" style="
                            background: #48bb78;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: 500;
                            margin: 0 8px 8px 0;
                            transition: all 0.2s;
                        " onmouseover="this.style.background='#38a169'" onmouseout="this.style.background='#48bb78'">
                            🧹 清除缓存
                        </button>
                    </div>
                    
                    <details style="
                        text-align: left;
                        background: #f7fafc;
                        border-radius: 8px;
                        padding: 16px;
                        margin-top: 24px;
                    ">
                        <summary style="
                            cursor: pointer;
                            font-weight: 500;
                            color: #4a5568;
                            margin-bottom: 12px;
                        ">💡 解决方案</summary>
                        
                        <ol style="
                            color: #718096;
                            font-size: 14px;
                            line-height: 1.5;
                            margin: 0;
                            padding-left: 20px;
                        ">
                            <li>按 <kbd>Ctrl+F5</kbd> 强制刷新页面</li>
                            <li>清除浏览器缓存和Cookie</li>
                            <li>尝试使用无痕/隐私模式访问</li>
                            <li>检查网络连接是否正常</li>
                            <li>尝试使用其他浏览器访问</li>
                        </ol>
                    </details>
                </div>
            </div>
            
            <script>
                function clearCacheAndReload() {
                    // 清除所有存储
                    sessionStorage.clear();
                    localStorage.clear();
                    
                    // 清除缓存API
                    if ('caches' in window) {
                        caches.keys().then(function(names) {
                            return Promise.all(
                                names.map(function(name) {
                                    return caches.delete(name);
                                })
                            );
                        }).then(function() {
                            window.location.reload(true);
                        });
                    } else {
                        window.location.reload(true);
                    }
                }
            </script>
        `;
    }
    
    // 尝试修复模块加载问题
    function attemptFix() {
        const errorData = getErrorData();
        
        if (errorData.count >= MAX_RETRIES) {
            console.error('❌ 已达到最大重试次数，显示错误页面');
            showErrorPage();
            return;
        }
        
        // 增加错误计数
        errorData.count++;
        errorData.timestamp = Date.now();
        saveErrorData(errorData);
        
        console.log(`🔄 尝试修复模块加载问题 (${errorData.count}/${MAX_RETRIES})`);
        
        // 清除相关缓存
        if ('caches' in window) {
            caches.keys().then(function(names) {
                const adminCaches = names.filter(name => 
                    name.includes('admin') || 
                    name.includes('vue') || 
                    name.includes('assets')
                );
                
                return Promise.all(adminCaches.map(name => caches.delete(name)));
            }).then(function() {
                console.log('🧹 已清除相关缓存');
                setTimeout(() => window.location.reload(true), RETRY_DELAY);
            }).catch(function() {
                setTimeout(() => window.location.reload(true), RETRY_DELAY);
            });
        } else {
            setTimeout(() => window.location.reload(true), RETRY_DELAY);
        }
    }
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        if (event.error && isModuleInitError(event.error)) {
            console.warn('🚨 检测到模块初始化错误:', event.error.message);
            event.preventDefault();
            attemptFix();
        }
    });
    
    // Promise错误处理
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && isModuleInitError(event.reason)) {
            console.warn('🚨 检测到Promise模块错误:', event.reason.message);
            event.preventDefault();
            attemptFix();
        }
    });
    
    // 监听应用成功加载
    function checkAppLoaded() {
        const app = document.getElementById('app');
        if (app && app.children.length > 0) {
            const hasLoadingIndicator = app.innerHTML.includes('正在加载管理后台');
            const hasErrorPage = app.innerHTML.includes('模块加载失败');
            
            if (!hasLoadingIndicator && !hasErrorPage) {
                console.log('✅ 应用加载成功，清除错误计数');
                clearErrorData();
                return true;
            }
        }
        return false;
    }
    
    // 定期检查应用状态
    let checkCount = 0;
    const maxChecks = 30; // 最多检查30次（15秒）
    
    const checkInterval = setInterval(function() {
        checkCount++;
        
        if (checkAppLoaded() || checkCount >= maxChecks) {
            clearInterval(checkInterval);
            
            if (checkCount >= maxChecks) {
                console.warn('⏰ 应用加载超时，可能存在问题');
                // 不自动显示错误页面，让用户手动处理
            }
        }
    }, 500);
    
    console.log('✅ 模块加载修复脚本已就绪');
})();
