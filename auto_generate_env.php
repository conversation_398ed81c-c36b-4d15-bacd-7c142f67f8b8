<?php

/**
 * 自动生成.env文件工具
 * 在部署过程中根据用户输入自动生成完整的.env配置文件
 */

class AutoEnvGenerator
{
    private $projectRoot;
    private $config = [];
    private $generatedKeys = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    /**
     * 记录日志
     */
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'INPUT' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 获取用户输入
     */
    private function getUserInput($prompt, $default = '', $required = true, $validator = null)
    {
        while (true) {
            if ($default) {
                echo "{$prompt} [{$default}]: ";
            } else {
                echo "{$prompt}: ";
            }

            $input = trim(fgets(STDIN));

            // 处理空输入
            if (empty($input)) {
                if (!empty($default)) {
                    return $default;
                } elseif ($required) {
                    $this->log('WARN', '此项为必填项，请输入有效值');
                    continue;
                } else {
                    return '';
                }
            }

            // 运行验证器
            if ($validator && is_callable($validator)) {
                $validationResult = $validator($input);
                if ($validationResult !== true) {
                    $this->log('WARN', $validationResult);
                    continue;
                }
            }

            return $input;
        }
    }
    
    /**
     * 生成随机密钥
     */
    private function generateRandomKey($length = 32)
    {
        return base64_encode(random_bytes($length));
    }
    
    /**
     * 生成Laravel APP_KEY
     */
    private function generateAppKey()
    {
        return 'base64:' . base64_encode(random_bytes(32));
    }
    
    /**
     * 生成JWT密钥
     */
    private function generateJwtSecret()
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection()
    {
        $this->log('INFO', '测试数据库连接...');

        try {
            $dsn = "mysql:host={$this->config['DB_HOST']};port={$this->config['DB_PORT']};charset={$this->config['DB_CHARSET']}";
            $pdo = new PDO($dsn, $this->config['DB_USERNAME'], $this->config['DB_PASSWORD']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // 测试数据库是否存在
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$this->config['DB_DATABASE']]);

            if ($stmt->rowCount() > 0) {
                $this->log('SUCCESS', '数据库连接测试成功，数据库存在');
            } else {
                $this->log('WARN', '数据库连接成功，但指定的数据库不存在');
                $this->log('WARN', '请确保数据库 "' . $this->config['DB_DATABASE'] . '" 已创建');
            }

            $pdo = null;
            return true;
        } catch (PDOException $e) {
            $this->log('ERROR', '数据库连接测试失败: ' . $e->getMessage());
            $this->log('WARN', '请检查数据库配置信息是否正确');

            // 不进行递归调用，直接返回false让用户手动检查
            return false;
        }
    }
    
    /**
     * 收集基础配置
     */
    private function collectBasicConfig()
    {
        $this->log('INPUT', '收集基础应用配置...');

        $this->config['APP_NAME'] = $this->getUserInput('应用名称', '晨鑫流量变现系统');
        $this->config['APP_ENV'] = $this->getUserInput('应用环境 (local/production)', 'production', true, function($input) {
            if (!in_array($input, ['local', 'production', 'testing'])) {
                return '请输入有效的环境类型: local, production, testing';
            }
            return true;
        });
        $this->config['APP_DEBUG'] = ($this->config['APP_ENV'] === 'production') ? 'false' : 'true';

        // 域名配置 - 添加验证
        $domain = $this->getUserInput('网站域名 (如: example.com)', '', true, function($input) {
            // 移除协议前缀
            $input = preg_replace('/^https?:\/\//', '', $input);
            $input = rtrim($input, '/');

            // 验证域名格式
            if (!preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $input)) {
                return '请输入有效的域名格式 (如: example.com)';
            }

            return true;
        });

        // 清理域名输入
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = rtrim($domain, '/');

        $this->config['APP_URL'] = 'https://' . $domain;
        $this->config['API_DOMAIN'] = $domain;
        $this->config['FRONTEND_URL'] = 'https://' . $domain;
        $this->config['SESSION_DOMAIN'] = '.' . $domain;

        // 自动生成密钥
        $this->config['APP_KEY'] = $this->generateAppKey();
        $this->config['JWT_SECRET'] = $this->generateJwtSecret();

        $this->generatedKeys[] = 'APP_KEY';
        $this->generatedKeys[] = 'JWT_SECRET';

        $this->log('SUCCESS', '基础配置收集完成');
    }
    
    /**
     * 收集数据库配置
     */
    private function collectDatabaseConfig()
    {
        $this->log('INPUT', '收集数据库配置...');

        $this->config['DB_CONNECTION'] = 'mysql';

        // 数据库主机验证
        $this->config['DB_HOST'] = $this->getUserInput('数据库主机', '127.0.0.1', true, function($input) {
            if (!filter_var($input, FILTER_VALIDATE_IP) && !preg_match('/^[a-zA-Z0-9.-]+$/', $input)) {
                return '请输入有效的IP地址或主机名';
            }
            return true;
        });

        // 数据库端口验证
        $this->config['DB_PORT'] = $this->getUserInput('数据库端口', '3306', true, function($input) {
            if (!is_numeric($input) || $input < 1 || $input > 65535) {
                return '请输入有效的端口号 (1-65535)';
            }
            return true;
        });

        // 数据库名称验证
        $this->config['DB_DATABASE'] = $this->getUserInput('数据库名称', '', true, function($input) {
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $input)) {
                return '数据库名称只能包含字母、数字和下划线';
            }
            if (strlen($input) > 64) {
                return '数据库名称不能超过64个字符';
            }
            return true;
        });

        // 数据库用户名验证
        $this->config['DB_USERNAME'] = $this->getUserInput('数据库用户名', '', true, function($input) {
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $input)) {
                return '用户名只能包含字母、数字和下划线';
            }
            if (strlen($input) > 32) {
                return '用户名不能超过32个字符';
            }
            return true;
        });

        // 数据库密码（可选）
        $this->config['DB_PASSWORD'] = $this->getUserInput('数据库密码', '', false);

        $this->config['DB_CHARSET'] = 'utf8mb4';
        $this->config['DB_COLLATION'] = 'utf8mb4_unicode_ci';
        $this->config['DB_STRICT'] = 'true';
        $this->config['DB_ENGINE'] = 'InnoDB';

        $this->log('SUCCESS', '数据库配置收集完成');
    }
    
    /**
     * 收集缓存和队列配置
     */
    private function collectCacheConfig()
    {
        $this->log('INPUT', '配置缓存和队列...');
        
        $useRedis = $this->getUserInput('是否使用Redis缓存? (y/n)', 'n');
        
        if (strtolower($useRedis) === 'y') {
            $this->config['CACHE_DRIVER'] = 'redis';
            $this->config['SESSION_DRIVER'] = 'redis';
            $this->config['QUEUE_CONNECTION'] = 'redis';
            
            $this->config['REDIS_HOST'] = $this->getUserInput('Redis主机', '127.0.0.1');
            $this->config['REDIS_PORT'] = $this->getUserInput('Redis端口', '6379');
            $this->config['REDIS_PASSWORD'] = $this->getUserInput('Redis密码', 'null', false);
            $this->config['REDIS_DB'] = '0';
        } else {
            $this->config['CACHE_DRIVER'] = 'file';
            $this->config['SESSION_DRIVER'] = 'file';
            $this->config['QUEUE_CONNECTION'] = 'sync';
        }
        
        $this->config['SESSION_LIFETIME'] = '120';
        $this->config['BROADCAST_DRIVER'] = 'log';
        $this->config['FILESYSTEM_DRIVER'] = 'local';
        
        $this->log('SUCCESS', '缓存配置完成');
    }
    
    /**
     * 收集邮件配置
     */
    private function collectMailConfig()
    {
        $this->log('INPUT', '配置邮件服务...');
        
        $enableMail = $this->getUserInput('是否配置邮件服务? (y/n)', 'n');
        
        if (strtolower($enableMail) === 'y') {
            $this->config['MAIL_MAILER'] = 'smtp';
            $this->config['MAIL_HOST'] = $this->getUserInput('SMTP主机', 'smtp.qq.com');
            $this->config['MAIL_PORT'] = $this->getUserInput('SMTP端口', '587');
            $this->config['MAIL_USERNAME'] = $this->getUserInput('邮箱用户名', '', true);
            $this->config['MAIL_PASSWORD'] = $this->getUserInput('邮箱密码', '', true);
            $this->config['MAIL_ENCRYPTION'] = $this->getUserInput('加密方式 (tls/ssl)', 'tls');
            $this->config['MAIL_FROM_ADDRESS'] = $this->config['MAIL_USERNAME'];
            $this->config['MAIL_FROM_NAME'] = $this->config['APP_NAME'];
        } else {
            $this->config['MAIL_MAILER'] = 'log';
            $this->config['MAIL_HOST'] = 'localhost';
            $this->config['MAIL_PORT'] = '25';
            $this->config['MAIL_USERNAME'] = '';
            $this->config['MAIL_PASSWORD'] = '';
            $this->config['MAIL_ENCRYPTION'] = 'null';
            $this->config['MAIL_FROM_ADDRESS'] = 'noreply@localhost';
            $this->config['MAIL_FROM_NAME'] = $this->config['APP_NAME'];
        }
        
        $this->log('SUCCESS', '邮件配置完成');
    }
    
    /**
     * 设置默认配置
     */
    private function setDefaultConfig()
    {
        $this->log('INFO', '设置默认配置项...');
        
        // 日志配置
        $this->config['LOG_CHANNEL'] = ($this->config['APP_ENV'] === 'production') ? 'daily' : 'single';
        $this->config['LOG_LEVEL'] = ($this->config['APP_ENV'] === 'production') ? 'error' : 'debug';
        $this->config['LOG_DEPRECATIONS_CHANNEL'] = 'null';
        
        // JWT配置
        $this->config['JWT_TTL'] = '1440';
        $this->config['JWT_REFRESH_TTL'] = '20160';
        $this->config['JWT_ALGO'] = 'HS256';
        $this->config['JWT_BLACKLIST_ENABLED'] = 'true';
        $this->config['JWT_BLACKLIST_GRACE_PERIOD'] = '0';
        
        // 防红系统配置
        $this->config['ANTI_BLOCK_ENABLED'] = 'true';
        $this->config['ANTI_BLOCK_CHECK_INTERVAL'] = '300';
        $this->config['ANTI_BLOCK_DOMAIN_POOL_SIZE'] = '10';
        $this->config['ANTI_BLOCK_FALLBACK_DOMAIN'] = 'https://www.baidu.com';
        $this->config['ANTI_BLOCK_ALERTS_ENABLED'] = 'true';
        $this->config['ANTI_BLOCK_WEBHOOK_URL'] = '';
        $this->config['ANTI_BLOCK_NOTIFICATION_EMAIL'] = '';
        $this->config['ANTI_BLOCK_IP_WHITELIST'] = '';
        $this->config['ANTI_BLOCK_IP_BLACKLIST'] = '';
        $this->config['ANTI_BLOCK_CDN_ENABLED'] = 'false';
        $this->config['ANTI_BLOCK_CDN_PROVIDER'] = 'cloudflare';
        
        // 支付配置
        $this->config['PAYMENT_ENABLED'] = 'true';
        $this->config['PAYMENT_DEFAULT'] = 'wechat';
        $this->config['PAYMENT_WECHAT_APPID'] = '';
        $this->config['PAYMENT_WECHAT_MCHID'] = '';
        $this->config['PAYMENT_WECHAT_KEY'] = '';
        $this->config['WECHAT_PAY_APP_ID'] = '';
        $this->config['WECHAT_PAY_MCH_ID'] = '';
        $this->config['WECHAT_PAY_KEY'] = '';
        $this->config['WECHAT_PAY_CERT_PATH'] = '/www/wwwroot/' . parse_url($this->config['APP_URL'], PHP_URL_HOST) . '/storage/certs/wechat';
        $this->config['WECHAT_PAY_KEY_PATH'] = '/www/wwwroot/' . parse_url($this->config['APP_URL'], PHP_URL_HOST) . '/storage/certs/wechat';
        $this->config['WECHAT_PAY_SANDBOX'] = 'false';
        
        // 支付宝配置
        $this->config['PAYMENT_ALIPAY_APPID'] = '';
        $this->config['PAYMENT_ALIPAY_KEY'] = '';
        $this->config['ALIPAY_APP_ID'] = '';
        $this->config['ALIPAY_PRIVATE_KEY'] = '';
        $this->config['ALIPAY_PUBLIC_KEY'] = '';
        $this->config['ALIPAY_SANDBOX'] = 'false';
        
        // 短信配置
        $this->config['SMS_PROVIDER'] = 'aliyun';
        $this->config['SMS_DRIVER'] = 'aliyun';
        $this->config['SMS_ENABLED'] = 'true';
        $this->config['SMS_TEST_MODE'] = 'false';
        $this->config['SMS_ALIYUN_ACCESS_KEY_ID'] = '';
        $this->config['SMS_ALIYUN_ACCESS_KEY_SECRET'] = '';
        $this->config['SMS_ALIYUN_SIGN_NAME'] = $this->config['APP_NAME'];
        $this->config['SMS_ALIYUN_TEMPLATE_CODE'] = '';
        $this->config['SMS_ALIYUN_REGION'] = 'cn-hangzhou';
        $this->config['SMS_ALIYUN_ENDPOINT'] = 'dysmsapi.aliyuncs.com';
        
        // 短信发送配置
        $this->config['SMS_VERIFY_CODE_LENGTH'] = '6';
        $this->config['SMS_VERIFY_CODE_EXPIRE'] = '5';
        $this->config['SMS_SEND_INTERVAL'] = '60';
        $this->config['SMS_DAILY_LIMIT'] = '10';
        $this->config['SMS_IP_DAILY_LIMIT'] = '100';
        $this->config['SMS_TIMEOUT'] = '30';
        
        // 管理员配置
        $this->config['ADMIN_EMAIL'] = 'admin@' . parse_url($this->config['APP_URL'], PHP_URL_HOST);
        $this->config['ALERT_WEBHOOK_URL'] = '';
        $this->config['SERVICE_TOKEN'] = '';
        
        // 系统监控配置
        $this->config['SYSTEM_MONITOR_ENABLED'] = 'true';
        $this->config['SYSTEM_MONITOR_INTERVAL'] = '300';
        $this->config['SYSTEM_MONITOR_ALERT_THRESHOLD'] = '80';
        $this->config['HEALTH_CHECK_ENABLED'] = 'true';
        $this->config['HEALTH_CHECK_INTERVAL'] = '60';
        $this->config['HEALTH_SCORE_THRESHOLD'] = '70';
        
        // IP地理位置服务配置
        $this->config['IP_LOCATION_CACHE_ENABLED'] = 'true';
        $this->config['IP_LOCATION_CACHE_TTL'] = '3600';
        $this->config['BAIDU_MAP_ENABLED'] = 'true';
        $this->config['BAIDU_MAP_API_KEY'] = 'VHr3iKqzTkSq4fBgK7ITGAyQ8FFZR0Om';
        $this->config['BAIDU_MAP_TIMEOUT'] = '5';
        $this->config['BAIDU_CLOUD_ENABLED'] = 'true';
        $this->config['BAIDU_CLOUD_TIMEOUT'] = '5';
        $this->config['IP_API_ENABLED'] = 'true';
        $this->config['IP_API_TIMEOUT'] = '5';
        $this->config['DEFAULT_CITY'] = '本地';
        
        // 群组营销功能配置
        $this->config['GROUP_MARKETING_ENABLED'] = 'true';
        $this->config['VIRTUAL_DATA_ENABLED'] = 'true';
        $this->config['CITY_REPLACE_ENABLED'] = 'true';
        $this->config['BROWSER_DETECTION_ENABLED'] = 'true';
        $this->config['ACCESS_LOG_ENABLED'] = 'true';
        $this->config['ACCESS_LOG_RETENTION_DAYS'] = '30';
        
        // 文件上传配置
        $this->config['UPLOAD_MAX_SIZE'] = '10240';
        $this->config['UPLOAD_ALLOWED_TYPES'] = 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx';
        $this->config['UPLOAD_PATH'] = 'uploads';
        $this->config['FILESYSTEM_DISK'] = 'local';
        
        // 操作日志配置
        $this->config['OPERATION_LOG_ENABLED'] = 'true';
        $this->config['OPERATION_LOG_RETENTION_DAYS'] = '90';
        
        // 宝塔环境特殊配置
        $this->config['BAOTA_ENABLED'] = 'true';
        $domain = parse_url($this->config['APP_URL'], PHP_URL_HOST);
        $this->config['PAYMENT_CERT_DIR'] = '/www/wwwroot/' . $domain . '/storage/certs';
        $this->config['PAYMENT_LOG_DIR'] = '/www/wwwroot/' . $domain . '/storage/logs/payment';
        $this->config['PAYMENT_BACKUP_DIR'] = '/www/backup/payment';
        
        // 队列和任务配置
        $this->config['QUEUE_FAILED_DRIVER'] = 'database-uuids';
        $this->config['HORIZON_ENABLED'] = 'false';
        
        // 缓存配置优化
        $this->config['CACHE_PREFIX'] = 'ffjq_';
        $this->config['CACHE_TTL'] = '3600';
        
        // 安全配置
        $this->config['SECURITY_RATE_LIMIT_ENABLED'] = 'true';
        $this->config['SECURITY_MAX_LOGIN_ATTEMPTS'] = '5';
        $this->config['SECURITY_LOGIN_LOCKOUT_TIME'] = '300';
        $this->config['SECURITY_PASSWORD_MIN_LENGTH'] = '6';
        
        // 调试和开发工具（生产环境关闭）
        $this->config['TELESCOPE_ENABLED'] = ($this->config['APP_ENV'] === 'production') ? 'false' : 'true';
        $this->config['DEBUGBAR_ENABLED'] = ($this->config['APP_ENV'] === 'production') ? 'false' : 'true';
        $this->config['TESTING_MODE'] = 'false';
        
        // 多语言配置
        $this->config['APP_LOCALE'] = 'zh_CN';
        $this->config['APP_FALLBACK_LOCALE'] = 'zh_CN';
        $this->config['APP_TIMEZONE'] = 'Asia/Shanghai';
        
        // API配置
        $this->config['API_RATE_LIMIT'] = '60';
        $this->config['API_THROTTLE_ENABLED'] = 'true';
        $this->config['API_VERSION'] = 'v1';
        
        // 数据统计配置
        $this->config['ANALYTICS_ENABLED'] = 'true';
        $this->config['ANALYTICS_RETENTION_DAYS'] = '365';
        
        // 备份配置
        $this->config['BACKUP_ENABLED'] = 'true';
        $this->config['BACKUP_RETENTION_DAYS'] = '30';
        $this->config['BACKUP_DISK'] = 'local';
        
        // 性能优化配置
        $this->config['OPCACHE_ENABLED'] = 'true';
        $this->config['QUERY_LOG_ENABLED'] = 'false';
        $this->config['SLOW_QUERY_LOG_ENABLED'] = 'true';
        $this->config['SLOW_QUERY_TIME'] = '2';
        
        $this->log('SUCCESS', '默认配置设置完成');
    }
    
    /**
     * 生成.env文件内容
     */
    private function generateEnvContent()
    {
        $content = "# 自动生成的环境配置文件\n";
        $content .= "# 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $content .= "# 请勿手动修改，如需更改请重新运行生成工具\n\n";
        
        // 按分组组织配置
        $groups = [
            '基础应用配置' => ['APP_NAME', 'APP_ENV', 'APP_KEY', 'APP_DEBUG', 'APP_URL'],
            '域名配置' => ['API_DOMAIN', 'FRONTEND_URL', 'SESSION_DOMAIN'],
            '日志配置' => ['LOG_CHANNEL', 'LOG_LEVEL', 'LOG_DEPRECATIONS_CHANNEL'],
            '数据库配置' => ['DB_CONNECTION', 'DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD', 'DB_CHARSET', 'DB_COLLATION', 'DB_STRICT', 'DB_ENGINE'],
            '缓存和队列配置' => ['BROADCAST_DRIVER', 'CACHE_DRIVER', 'FILESYSTEM_DRIVER', 'QUEUE_CONNECTION', 'SESSION_DRIVER', 'SESSION_LIFETIME'],
        ];
        
        // 如果使用Redis，添加Redis配置组
        if (isset($this->config['REDIS_HOST'])) {
            $groups['Redis配置'] = ['REDIS_HOST', 'REDIS_PASSWORD', 'REDIS_PORT', 'REDIS_DB'];
        }
        
        $groups['邮件配置'] = ['MAIL_MAILER', 'MAIL_HOST', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_PASSWORD', 'MAIL_ENCRYPTION', 'MAIL_FROM_ADDRESS', 'MAIL_FROM_NAME'];
        
        foreach ($groups as $groupName => $keys) {
            $content .= "# {$groupName}\n";
            foreach ($keys as $key) {
                if (isset($this->config[$key])) {
                    $value = $this->config[$key];
                    // 如果值包含空格或特殊字符，用引号包围
                    if (strpos($value, ' ') !== false || strpos($value, '#') !== false) {
                        $value = '"' . $value . '"';
                    }
                    $content .= "{$key}={$value}\n";
                }
            }
            $content .= "\n";
        }
        
        // 添加其他所有配置
        $content .= "# 其他配置\n";
        $usedKeys = array_merge(...array_values($groups));
        foreach ($this->config as $key => $value) {
            if (!in_array($key, $usedKeys)) {
                if (strpos($value, ' ') !== false || strpos($value, '#') !== false) {
                    $value = '"' . $value . '"';
                }
                $content .= "{$key}={$value}\n";
            }
        }
        
        return $content;
    }
    
    /**
     * 保存.env文件
     */
    private function saveEnvFile()
    {
        $envFile = $this->projectRoot . '/.env';
        $content = $this->generateEnvContent();
        
        // 备份现有.env文件
        if (file_exists($envFile)) {
            $backupFile = $envFile . '.backup.' . date('YmdHis');
            copy($envFile, $backupFile);
            $this->log('INFO', "已备份现有.env文件到: " . basename($backupFile));
        }
        
        // 写入新的.env文件
        if (file_put_contents($envFile, $content)) {
            $this->log('SUCCESS', '.env文件生成成功');
            return true;
        } else {
            $this->log('ERROR', '.env文件生成失败');
            return false;
        }
    }
    
    /**
     * 显示生成摘要
     */
    private function showSummary()
    {
        echo "\n";
        echo "============================================================\n";
        echo "                    .env文件生成摘要\n";
        echo "============================================================\n";
        echo "\n";
        echo "✅ 配置项总数: " . count($this->config) . " 个\n";
        echo "✅ 自动生成密钥: " . count($this->generatedKeys) . " 个\n";
        echo "✅ 应用名称: " . $this->config['APP_NAME'] . "\n";
        echo "✅ 应用环境: " . $this->config['APP_ENV'] . "\n";
        echo "✅ 网站域名: " . $this->config['APP_URL'] . "\n";
        echo "✅ 数据库名: " . $this->config['DB_DATABASE'] . "\n";
        echo "✅ 数据库用户: " . $this->config['DB_USERNAME'] . "\n";
        echo "✅ 缓存驱动: " . $this->config['CACHE_DRIVER'] . "\n";
        echo "\n";
        echo "🔑 自动生成的密钥:\n";
        foreach ($this->generatedKeys as $key) {
            echo "   - {$key}\n";
        }
        echo "\n";
        echo "📝 下一步操作:\n";
        echo "   1. 检查.env文件内容\n";
        echo "   2. 根据需要配置支付和短信服务\n";
        echo "   3. 运行部署脚本\n";
        echo "\n";
    }
    
    /**
     * 运行自动生成流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🤖 .env文件自动生成工具                           ║\n";
        echo "║                                                          ║\n";
        echo "║    根据用户输入自动生成完整的环境配置文件                 ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        try {
            // 收集配置
            $this->collectBasicConfig();
            $this->collectDatabaseConfig();
            $this->collectCacheConfig();
            $this->collectMailConfig();
            $this->setDefaultConfig();
            
            // 生成文件
            if ($this->saveEnvFile()) {
                // 测试数据库连接
                $this->log('INFO', '测试数据库连接...');
                $dbConnected = $this->testDatabaseConnection();

                $this->showSummary();

                if (!$dbConnected) {
                    echo "\n⚠️  注意: 数据库连接测试失败，请检查数据库配置\n";
                }

                $endTime = microtime(true);
                $duration = round($endTime - $startTime, 2);

                echo "🎉 .env文件自动生成完成！耗时: {$duration}秒\n";
                echo "\n";
                return true;
            } else {
                $this->log('ERROR', '.env文件生成失败');
                return false;
            }
            
        } catch (Exception $e) {
            $this->log('ERROR', '生成过程中出现异常: ' . $e->getMessage());
            return false;
        }
    }
}

// 运行自动生成工具
if (php_sapi_name() === 'cli') {
    $generator = new AutoEnvGenerator();
    $success = $generator->run();
    exit($success ? 0 : 1);
}
