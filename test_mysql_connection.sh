#!/bin/bash

# 测试MySQL连接修复
# 用于验证空密码情况下的MySQL连接

echo "测试MySQL连接修复..."

# 模拟变量
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_USER="ffjq"
DB_PASSWORD=""  # 空密码
DB_NAME="ffjq"

echo "测试参数:"
echo "  主机: $DB_HOST"
echo "  端口: $DB_PORT"
echo "  用户: $DB_USER"
echo "  密码: [空]"
echo "  数据库: $DB_NAME"
echo ""

# 旧的方式（会失败）
echo "1. 旧的连接方式（预期失败）:"
echo "   命令: mysql -h\"$DB_HOST\" -P\"$DB_PORT\" -u\"$DB_USER\" -p\"$DB_PASSWORD\" -e \"SELECT 1;\""
if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" 2>/dev/null; then
    echo "   结果: ✅ 成功"
else
    echo "   结果: ❌ 失败（预期）"
fi
echo ""

# 新的方式（应该成功）
echo "2. 新的连接方式（预期成功）:"
mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
if [ -n "$DB_PASSWORD" ]; then
    mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
fi
echo "   命令: $mysql_cmd -e \"SELECT 1;\""
if $mysql_cmd -e "SELECT 1;" 2>/dev/null; then
    echo "   结果: ✅ 成功"
else
    echo "   结果: ❌ 失败"
fi
echo ""

echo "测试完成！"
