<?php

/**
 * 宝塔部署优化工具
 * 验证项目结构和依赖，确保适合宝塔面板部署
 */

class BaotaDeploymentOptimizer
{
    private $projectRoot;
    private $issues = [];
    private $fixes = [];
    private $checks = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    /**
     * 记录日志
     */
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'CHECK' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 检查PHP版本兼容性
     */
    public function checkPhpVersion()
    {
        $this->log('CHECK', '检查PHP版本兼容性...');
        
        $currentVersion = PHP_VERSION;
        $requiredVersion = '8.1.0';
        
        if (version_compare($currentVersion, $requiredVersion, '>=')) {
            $this->log('SUCCESS', "PHP版本 {$currentVersion} 符合要求 (>= {$requiredVersion})");
            $this->checks[] = "✅ PHP版本兼容 ({$currentVersion})";
            return true;
        } else {
            $this->log('ERROR', "PHP版本 {$currentVersion} 不符合要求 (需要 >= {$requiredVersion})");
            $this->issues[] = "PHP版本过低，需要升级到 {$requiredVersion} 或更高版本";
            $this->checks[] = "❌ PHP版本不兼容 ({$currentVersion})";
            return false;
        }
    }
    
    /**
     * 检查必需的PHP扩展
     */
    public function checkPhpExtensions()
    {
        $this->log('CHECK', '检查PHP扩展...');
        
        $requiredExtensions = [
            'pdo' => 'PDO数据库扩展',
            'pdo_mysql' => 'MySQL PDO扩展',
            'mbstring' => '多字节字符串扩展',
            'openssl' => 'OpenSSL加密扩展',
            'tokenizer' => 'Tokenizer扩展',
            'xml' => 'XML扩展',
            'ctype' => 'Ctype扩展',
            'json' => 'JSON扩展',
            'bcmath' => 'BCMath数学扩展',
            'curl' => 'cURL扩展',
            'fileinfo' => 'Fileinfo扩展',
            'gd' => 'GD图像处理扩展',
            'zip' => 'ZIP压缩扩展'
        ];
        
        $missingExtensions = [];
        
        foreach ($requiredExtensions as $extension => $description) {
            if (extension_loaded($extension)) {
                $this->log('SUCCESS', "✅ {$description} 已安装");
                $this->checks[] = "✅ {$description}";
            } else {
                $this->log('ERROR', "❌ {$description} 未安装");
                $missingExtensions[] = $extension;
                $this->issues[] = "缺少PHP扩展: {$extension} ({$description})";
                $this->checks[] = "❌ {$description}";
            }
        }
        
        return empty($missingExtensions);
    }
    
    /**
     * 检查项目结构
     */
    public function checkProjectStructure()
    {
        $this->log('CHECK', '检查项目结构...');
        
        $requiredDirs = [
            'app' => '应用程序目录',
            'config' => '配置文件目录',
            'database' => '数据库文件目录',
            'public' => '公共文件目录',
            'resources' => '资源文件目录',
            'routes' => '路由文件目录',
            'storage' => '存储目录',
            'vendor' => 'Composer依赖目录'
        ];
        
        $requiredFiles = [
            'artisan' => 'Laravel命令行工具',
            'composer.json' => 'Composer配置文件',
            'composer.lock' => 'Composer锁定文件',
            'public/index.php' => '应用入口文件'
        ];
        
        $structureOk = true;
        
        // 检查目录
        foreach ($requiredDirs as $dir => $description) {
            $path = $this->projectRoot . '/' . $dir;
            if (is_dir($path)) {
                $this->log('SUCCESS', "✅ {$description} 存在");
                $this->checks[] = "✅ {$description}";
            } else {
                $this->log('ERROR', "❌ {$description} 不存在");
                $this->issues[] = "缺少目录: {$dir}";
                $this->checks[] = "❌ {$description}";
                $structureOk = false;
            }
        }
        
        // 检查文件
        foreach ($requiredFiles as $file => $description) {
            $path = $this->projectRoot . '/' . $file;
            if (file_exists($path)) {
                $this->log('SUCCESS', "✅ {$description} 存在");
                $this->checks[] = "✅ {$description}";
            } else {
                $this->log('ERROR', "❌ {$description} 不存在");
                $this->issues[] = "缺少文件: {$file}";
                $this->checks[] = "❌ {$description}";
                $structureOk = false;
            }
        }
        
        return $structureOk;
    }
    
    /**
     * 检查文件权限
     */
    public function checkFilePermissions()
    {
        $this->log('CHECK', '检查文件权限...');
        
        $writableDirs = [
            'storage',
            'storage/app',
            'storage/framework',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'storage/logs',
            'bootstrap/cache'
        ];
        
        $permissionsOk = true;
        
        foreach ($writableDirs as $dir) {
            $path = $this->projectRoot . '/' . $dir;
            
            if (!is_dir($path)) {
                $this->log('WARN', "目录不存在，尝试创建: {$dir}");
                if (mkdir($path, 0755, true)) {
                    $this->log('SUCCESS', "创建目录成功: {$dir}");
                    $this->fixes[] = "创建了目录: {$dir}";
                } else {
                    $this->log('ERROR', "创建目录失败: {$dir}");
                    $this->issues[] = "无法创建目录: {$dir}";
                    $permissionsOk = false;
                    continue;
                }
            }
            
            if (is_writable($path)) {
                $this->log('SUCCESS', "✅ {$dir} 目录可写");
                $this->checks[] = "✅ {$dir} 可写";
            } else {
                $this->log('ERROR', "❌ {$dir} 目录不可写");
                $this->issues[] = "目录不可写: {$dir}";
                $this->checks[] = "❌ {$dir} 不可写";
                $permissionsOk = false;
                
                // 尝试修复权限
                if (chmod($path, 0755)) {
                    $this->log('SUCCESS', "修复权限成功: {$dir}");
                    $this->fixes[] = "修复了目录权限: {$dir}";
                } else {
                    $this->log('WARN', "修复权限失败: {$dir}");
                }
            }
        }
        
        return $permissionsOk;
    }
    
    /**
     * 检查Composer依赖
     */
    public function checkComposerDependencies()
    {
        $this->log('CHECK', '检查Composer依赖...');
        
        if (!file_exists($this->projectRoot . '/vendor/autoload.php')) {
            $this->log('ERROR', 'Composer依赖未安装');
            $this->issues[] = 'Composer依赖未安装，需要运行 composer install';
            $this->checks[] = '❌ Composer依赖未安装';
            return false;
        }
        
        $this->log('SUCCESS', '✅ Composer依赖已安装');
        $this->checks[] = '✅ Composer依赖已安装';
        
        // 检查关键依赖
        $keyPackages = [
            'laravel/framework' => 'Laravel框架',
            'tymon/jwt-auth' => 'JWT认证',
            'spatie/laravel-permission' => '权限管理',
            'intervention/image' => '图像处理'
        ];
        
        $composerLock = json_decode(file_get_contents($this->projectRoot . '/composer.lock'), true);
        $installedPackages = [];
        
        foreach ($composerLock['packages'] as $package) {
            $installedPackages[$package['name']] = $package['version'];
        }
        
        foreach ($keyPackages as $package => $description) {
            if (isset($installedPackages[$package])) {
                $version = $installedPackages[$package];
                $this->log('SUCCESS', "✅ {$description} ({$version})");
                $this->checks[] = "✅ {$description}";
            } else {
                $this->log('WARN', "⚠️  {$description} 未找到");
                $this->checks[] = "⚠️  {$description} 未找到";
            }
        }
        
        return true;
    }
    
    /**
     * 检查宝塔环境特殊配置
     */
    public function checkBaotaSpecificConfig()
    {
        $this->log('CHECK', '检查宝塔环境特殊配置...');
        
        // 检查.htaccess文件（Apache环境）
        $htaccessFile = $this->projectRoot . '/public/.htaccess';
        if (file_exists($htaccessFile)) {
            $this->log('SUCCESS', '✅ .htaccess文件存在');
            $this->checks[] = '✅ Apache重写规则';
        } else {
            $this->log('WARN', '⚠️  .htaccess文件不存在，可能影响URL重写');
            $this->checks[] = '⚠️  Apache重写规则缺失';
        }
        
        // 检查nginx配置文件
        $nginxConfigs = glob($this->projectRoot . '/nginx/*.conf');
        if (!empty($nginxConfigs)) {
            $this->log('SUCCESS', '✅ 发现Nginx配置文件');
            $this->checks[] = '✅ Nginx配置文件';
        } else {
            $this->log('INFO', 'ℹ️  未发现Nginx配置文件');
            $this->checks[] = 'ℹ️  Nginx配置文件';
        }
        
        // 检查存储链接
        $storageLink = $this->projectRoot . '/public/storage';
        if (is_link($storageLink)) {
            $this->log('SUCCESS', '✅ 存储链接已创建');
            $this->checks[] = '✅ 存储链接';
        } else {
            $this->log('WARN', '⚠️  存储链接未创建');
            $this->issues[] = '存储链接未创建，需要运行 php artisan storage:link';
            $this->fixes[] = '需要创建存储链接';
            $this->checks[] = '⚠️  存储链接未创建';
        }
        
        return true;
    }
    
    /**
     * 运行所有检查
     */
    public function runAllChecks()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔍 宝塔部署优化检查工具                           ║\n";
        echo "║                                                          ║\n";
        echo "║    验证项目结构和依赖，确保适合宝塔面板部署               ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 运行所有检查
        $phpVersionOk = $this->checkPhpVersion();
        $extensionsOk = $this->checkPhpExtensions();
        $structureOk = $this->checkProjectStructure();
        $permissionsOk = $this->checkFilePermissions();
        $composerOk = $this->checkComposerDependencies();
        $baotaOk = $this->checkBaotaSpecificConfig();
        
        $allChecksPass = $phpVersionOk && $extensionsOk && $structureOk && $permissionsOk && $composerOk && $baotaOk;
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        echo "\n";
        echo "============================================================\n";
        echo "                    检查结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if ($allChecksPass && empty($this->issues)) {
            echo "🎉 宝塔部署检查全部通过！\n";
        } else {
            echo "⚠️  发现 " . count($this->issues) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 检查统计:\n";
        echo "   检查项目: " . count($this->checks) . " 项\n";
        echo "   发现问题: " . count($this->issues) . " 个\n";
        echo "   自动修复: " . count($this->fixes) . " 个\n";
        echo "   检查耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->issues)) {
            echo "❌ 需要注意的问题:\n";
            foreach ($this->issues as $issue) {
                echo "   - {$issue}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        echo "📋 详细检查结果:\n";
        foreach ($this->checks as $check) {
            echo "   {$check}\n";
        }
        echo "\n";
        
        echo "🚀 下一步操作:\n";
        if ($allChecksPass && empty($this->issues)) {
            echo "   1. 运行环境配置检查: php check_env_config.php\n";
            echo "   2. 执行部署脚本: bash 使用现有数据库部署.sh\n";
            echo "   3. 访问网站验证部署结果\n";
        } else {
            echo "   1. 解决上述问题\n";
            echo "   2. 重新运行检查: php baota_deployment_optimizer.php\n";
            echo "   3. 问题解决后再执行部署\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 确保所有检查项都通过后再进行部署。\n";
        echo "\n";
        
        return $allChecksPass && empty($this->issues);
    }
}

// 运行检查工具
if (php_sapi_name() === 'cli') {
    $optimizer = new BaotaDeploymentOptimizer();
    $success = $optimizer->runAllChecks();
    exit($success ? 0 : 1);
}
