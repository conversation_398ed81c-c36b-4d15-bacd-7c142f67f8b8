<?php
/**
 * 部署前检查脚本
 * 检查可能导致部署失败的问题
 */

class PreDeployChecker
{
    private $projectRoot;
    private $issues = [];
    private $warnings = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    public function runAllChecks()
    {
        echo "🔍 开始部署前检查...\n\n";
        
        $this->checkEnvironmentFile();
        $this->checkMigrationFiles();
        $this->checkFrontendFiles();
        $this->checkPermissions();
        $this->checkDependencies();
        
        $this->displayResults();
        
        return empty($this->issues);
    }
    
    private function checkEnvironmentFile()
    {
        echo "📋 检查环境配置文件...\n";
        
        $envFile = $this->projectRoot . '/.env';
        if (!file_exists($envFile)) {
            $this->issues[] = ".env 文件不存在";
            return;
        }
        
        $envContent = file_get_contents($envFile);
        
        // 检查必要的配置项
        $requiredKeys = [
            'APP_KEY', 'DB_HOST', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD', 'JWT_SECRET'
        ];
        
        foreach ($requiredKeys as $key) {
            if (!preg_match("/^{$key}=.+$/m", $envContent)) {
                $this->issues[] = ".env 文件缺少必要配置: $key";
            }
        }
        
        // 检查 APP_KEY 格式
        if (preg_match('/^APP_KEY=base64:([A-Za-z0-9+\/=]+)$/m', $envContent)) {
            echo "  ✅ APP_KEY 格式正确\n";
        } else {
            $this->issues[] = "APP_KEY 格式不正确或为空";
        }
        
        echo "  ✅ 环境配置检查完成\n\n";
    }
    
    private function checkMigrationFiles()
    {
        echo "🗃️  检查数据库迁移文件...\n";
        
        $migrationDir = $this->projectRoot . '/database/migrations';
        if (!is_dir($migrationDir)) {
            $this->issues[] = "迁移目录不存在: $migrationDir";
            return;
        }
        
        $migrationFiles = glob($migrationDir . '/*.php');
        $checkedFiles = 0;
        $fixedFiles = 0;
        
        foreach ($migrationFiles as $file) {
            $content = file_get_contents($file);
            $filename = basename($file);
            
            // 检查是否有 Schema::create 但没有表存在性检查
            if (preg_match('/Schema::create\([\'"]([^\'"]+)[\'"]/', $content, $matches)) {
                $tableName = $matches[1];
                
                if (!preg_match('/Schema::hasTable\([\'"]' . preg_quote($tableName) . '[\'"]/', $content)) {
                    $this->warnings[] = "迁移文件 $filename 创建表 '$tableName' 但没有检查表是否已存在";
                    
                    // 检查是否已经修复
                    if (preg_match('/if\s*\(\s*!\s*Schema::hasTable/', $content)) {
                        $fixedFiles++;
                        echo "  ✅ $filename 已修复表存在性检查\n";
                    }
                } else {
                    echo "  ✅ $filename 包含表存在性检查\n";
                }
            }
            
            // 检查语法错误
            if (!$this->checkPHPSyntax($file)) {
                $this->issues[] = "迁移文件语法错误: $filename";
            }
            
            $checkedFiles++;
        }
        
        echo "  📊 检查了 $checkedFiles 个迁移文件，$fixedFiles 个已修复\n\n";
    }
    
    private function checkFrontendFiles()
    {
        echo "🌐 检查前端文件...\n";
        
        $indexFile = $this->projectRoot . '/public/admin/index.html';
        if (!file_exists($indexFile)) {
            $this->issues[] = "前端入口文件不存在: $indexFile";
            return;
        }
        
        $content = file_get_contents($indexFile);
        
        // 检查是否包含导致无限刷新的脚本
        if (strpos($content, 'vue-error-handler.js') !== false) {
            if (strpos($content, 'window.location.reload') !== false) {
                $this->warnings[] = "前端可能包含导致无限刷新的错误处理脚本";
            }
        }
        
        // 检查是否是简化版本（修复后的版本）
        if (strpos($content, '<div id="app"></div>') !== false && 
            strpos($content, 'modulepreload') !== false) {
            echo "  ✅ 前端入口文件已修复\n";
        } else {
            $this->warnings[] = "前端入口文件可能需要优化";
        }
        
        // 检查前端环境配置
        $frontendEnv = $this->projectRoot . '/admin/.env';
        if (file_exists($frontendEnv)) {
            $envContent = file_get_contents($frontendEnv);
            if (strpos($envContent, 'localhost') !== false) {
                $this->warnings[] = "前端环境配置仍指向本地开发环境";
            } else {
                echo "  ✅ 前端环境配置正确\n";
            }
        }
        
        echo "  ✅ 前端文件检查完成\n\n";
    }
    
    private function checkPermissions()
    {
        echo "🔐 检查文件权限...\n";
        
        $writableDirs = [
            'storage',
            'storage/logs',
            'storage/framework',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'bootstrap/cache'
        ];
        
        foreach ($writableDirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (!is_dir($fullPath)) {
                $this->warnings[] = "目录不存在: $dir";
            } elseif (!is_writable($fullPath)) {
                $this->issues[] = "目录不可写: $dir";
            } else {
                echo "  ✅ $dir 权限正确\n";
            }
        }
        
        echo "  ✅ 权限检查完成\n\n";
    }
    
    private function checkDependencies()
    {
        echo "📦 检查依赖包...\n";
        
        $composerLock = $this->projectRoot . '/composer.lock';
        if (!file_exists($composerLock)) {
            $this->warnings[] = "composer.lock 不存在，建议运行 composer install";
        } else {
            echo "  ✅ Composer 依赖已锁定\n";
        }
        
        $vendorDir = $this->projectRoot . '/vendor';
        if (!is_dir($vendorDir)) {
            $this->issues[] = "vendor 目录不存在，需要运行 composer install";
        } else {
            echo "  ✅ Vendor 目录存在\n";
        }
        
        echo "  ✅ 依赖检查完成\n\n";
    }
    
    private function checkPHPSyntax($file)
    {
        $output = [];
        $returnCode = 0;
        exec("php -l " . escapeshellarg($file) . " 2>&1", $output, $returnCode);
        return $returnCode === 0;
    }
    
    private function displayResults()
    {
        echo "📊 检查结果汇总:\n";
        echo "================\n\n";
        
        if (empty($this->issues) && empty($this->warnings)) {
            echo "🎉 所有检查都通过了！可以安全部署。\n";
            return;
        }
        
        if (!empty($this->issues)) {
            echo "❌ 发现 " . count($this->issues) . " 个严重问题（必须修复）:\n";
            foreach ($this->issues as $issue) {
                echo "   • $issue\n";
            }
            echo "\n";
        }
        
        if (!empty($this->warnings)) {
            echo "⚠️  发现 " . count($this->warnings) . " 个警告（建议修复）:\n";
            foreach ($this->warnings as $warning) {
                echo "   • $warning\n";
            }
            echo "\n";
        }
        
        if (!empty($this->issues)) {
            echo "💡 建议：请先修复所有严重问题后再进行部署。\n";
        } else {
            echo "💡 建议：虽然有一些警告，但可以继续部署。建议在部署后修复警告项。\n";
        }
    }
}

// 执行检查
try {
    $checker = new PreDeployChecker();
    $success = $checker->runAllChecks();
    
    exit($success ? 0 : 1);
} catch (Exception $e) {
    echo "💥 检查过程中出现错误: " . $e->getMessage() . "\n";
    exit(1);
}
