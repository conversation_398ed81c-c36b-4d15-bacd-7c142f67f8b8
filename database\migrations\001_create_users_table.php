<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 用户表迁移
 * 支持多角色用户管理：超级管理员、分站管理员、分销商等
 */
return new class extends Migration
{
    /**
     * 创建用户表
     */
    public function up()
    {
        // 检查表是否已存在，如果不存在才创建
        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('email', 100)->unique()->nullable()->comment('邮箱');
            $table->timestamp('email_verified_at')->nullable()->comment('邮箱验证时间');
            $table->string('phone', 20)->nullable()->comment('手机号');
            $table->string('password')->comment('密码');
            $table->string('avatar')->nullable()->comment('头像');
            $table->string('nickname', 50)->nullable()->comment('昵称');
            $table->enum('role', ['admin', 'substation', 'distributor', 'user'])->default('user')->comment('角色：admin-超级管理员,substation-分站管理员,distributor-分销商,user-普通用户');
            $table->tinyInteger('status')->default(1)->comment('状态：1-正常,2-禁用,3-到期');
            $table->decimal('balance', 10, 2)->default(0)->comment('余额');
            $table->decimal('frozen_balance', 10, 2)->default(0)->comment('冻结余额');
            $table->decimal('total_commission', 10, 2)->default(0)->comment('累计佣金');
            $table->bigInteger('parent_id')->nullable()->comment('上级用户ID');
            $table->integer('distributor_level')->nullable()->comment('分销商等级');
            $table->bigInteger('substation_id')->nullable()->comment('分站ID');
            $table->string('invite_code', 20)->unique()->nullable()->comment('邀请码');
            $table->bigInteger('invited_by')->nullable()->comment('邀请人ID');
            $table->timestamp('phone_verified_at')->nullable()->comment('手机验证时间');
            $table->timestamp('last_login_at')->nullable()->comment('最后登录时间');
            $table->string('last_login_ip', 45)->nullable()->comment('最后登录IP');
            $table->integer('login_count')->default(0)->comment('登录次数');
            $table->text('remark')->nullable()->comment('备注');
            $table->json('extra_data')->nullable()->comment('扩展数据');
            $table->timestamps();
            $table->softDeletes()->comment('软删除时间');
            
            // 索引
            $table->index(['role', 'status']);
            $table->index(['parent_id']);
            $table->index(['invite_code']);
            $table->index(['deleted_at']);
        });
        }
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}; 