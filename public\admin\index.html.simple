<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">
    
    <!-- 最简单的错误处理 -->
    <script>
      // 静默所有错误
      window.onerror = function() { return true; };
      window.addEventListener('unhandledrejection', function(e) { e.preventDefault(); });
      
      // 简单的超时检测
      setTimeout(function() {
        const app = document.getElementById('app');
        if (app && app.innerHTML.includes('管理后台启动中')) {
          app.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: Arial, sans-serif;">
              <div style="background: white; padding: 40px; border-radius: 12px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                <h2 style="color: #e74c3c; margin-bottom: 20px;">🔧 系统维护中</h2>
                <p style="color: #666; margin-bottom: 20px;">前端模块正在优化，请稍后再试</p>
                <button onclick="location.reload(true)" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">重新加载</button>
              </div>
            </div>
          `;
        }
      }, 10000); // 10秒后检查
    </script>
  </head>
  <body>
    <div id="app">
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: Arial, sans-serif;">
        <div style="background: white; padding: 40px; border-radius: 12px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
          <div style="width: 50px; height: 50px; border: 3px solid #f3f3f3; border-top: 3px solid #409EFF; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
          <h3 style="color: #333; margin-bottom: 10px;">晨鑫流量变现</h3>
          <p style="color: #666;">管理后台启动中...</p>
        </div>
      </div>
      <style>
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        body { margin: 0; padding: 0; }
      </style>
    </div>
    
    <!-- 延迟加载主应用 -->
    <script>
      setTimeout(function() {
        const script = document.createElement('script');
        script.type = 'module';
        script.crossOrigin = 'anonymous';
        script.src = '/admin/assets/index-BWOuGn0N.js';
        document.head.appendChild(script);
      }, 500);
    </script>
  </body>
</html>
