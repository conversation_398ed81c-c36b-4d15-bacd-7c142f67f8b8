<?php

/**
 * 环境配置检查和修复工具
 * 检查.env文件的完整性，确保宝塔部署成功
 */

class EnvConfigChecker
{
    private $projectRoot;
    private $requiredKeys = [];
    private $issues = [];
    private $fixes = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
        $this->initRequiredKeys();
    }
    
    /**
     * 初始化必需的配置项
     */
    private function initRequiredKeys()
    {
        $this->requiredKeys = [
            // 基础应用配置
            'APP_NAME' => '应用名称',
            'APP_ENV' => '应用环境',
            'APP_KEY' => '应用密钥',
            'APP_DEBUG' => '调试模式',
            'APP_URL' => '应用URL',
            
            // 数据库配置
            'DB_CONNECTION' => '数据库连接类型',
            'DB_HOST' => '数据库主机',
            'DB_PORT' => '数据库端口',
            'DB_DATABASE' => '数据库名称',
            'DB_USERNAME' => '数据库用户名',
            'DB_PASSWORD' => '数据库密码',
            
            // JWT配置
            'JWT_SECRET' => 'JWT密钥',
            'JWT_TTL' => 'JWT过期时间',
            
            // 缓存配置
            'CACHE_DRIVER' => '缓存驱动',
            'SESSION_DRIVER' => '会话驱动',
            'QUEUE_CONNECTION' => '队列连接',
            
            // 日志配置
            'LOG_CHANNEL' => '日志通道',
            'LOG_LEVEL' => '日志级别',
        ];
    }
    
    /**
     * 记录日志
     */
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'CHECK' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 加载环境变量
     */
    private function loadEnvFile($filePath)
    {
        if (!file_exists($filePath)) {
            return [];
        }
        
        $env = [];
        $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || str_starts_with($line, '#')) {
                continue;
            }
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $env[trim($key)] = trim($value, '"\'');
            }
        }
        
        return $env;
    }
    
    /**
     * 检查.env文件
     */
    public function checkEnvFile()
    {
        $this->log('CHECK', '检查环境配置文件...');
        
        $envFile = $this->projectRoot . '/.env';
        $envExampleFile = $this->projectRoot . '/.env.example';
        $envBaotaFile = $this->projectRoot . '/.env.baota';
        
        // 检查.env文件是否存在
        if (!file_exists($envFile)) {
            $this->issues[] = '.env文件不存在';
            $this->log('ERROR', '.env文件不存在');
            
            if (file_exists($envBaotaFile)) {
                $this->log('INFO', '发现.env.baota模板文件，建议复制为.env');
                $this->fixes[] = '复制.env.baota为.env文件';
            } elseif (file_exists($envExampleFile)) {
                $this->log('INFO', '发现.env.example文件，建议复制为.env');
                $this->fixes[] = '复制.env.example为.env文件';
            }
            return false;
        }
        
        // 加载.env文件
        $env = $this->loadEnvFile($envFile);
        $this->log('SUCCESS', '成功加载.env文件，包含 ' . count($env) . ' 个配置项');
        
        // 检查必需的配置项
        $missingKeys = [];
        $emptyKeys = [];
        
        foreach ($this->requiredKeys as $key => $description) {
            if (!isset($env[$key])) {
                $missingKeys[] = $key;
                $this->log('ERROR', "缺少必需配置项: {$key} ({$description})");
            } elseif (empty($env[$key]) && $key !== 'APP_DEBUG') {
                $emptyKeys[] = $key;
                $this->log('WARN', "配置项为空: {$key} ({$description})");
            }
        }
        
        if (!empty($missingKeys)) {
            $this->issues[] = '缺少 ' . count($missingKeys) . ' 个必需配置项';
        }
        
        if (!empty($emptyKeys)) {
            $this->issues[] = '有 ' . count($emptyKeys) . ' 个配置项为空';
        }
        
        // 检查特殊配置项
        $this->checkSpecialConfigs($env);
        
        return empty($missingKeys);
    }
    
    /**
     * 检查特殊配置项
     */
    private function checkSpecialConfigs($env)
    {
        // 检查APP_KEY
        if (isset($env['APP_KEY']) && (empty($env['APP_KEY']) || $env['APP_KEY'] === 'base64:')) {
            $this->issues[] = 'APP_KEY未设置或无效';
            $this->log('ERROR', 'APP_KEY未设置，需要运行 php artisan key:generate');
            $this->fixes[] = '运行 php artisan key:generate 生成应用密钥';
        }
        
        // 检查JWT_SECRET
        if (isset($env['JWT_SECRET']) && empty($env['JWT_SECRET'])) {
            $this->issues[] = 'JWT_SECRET未设置';
            $this->log('ERROR', 'JWT_SECRET未设置，需要运行 php artisan jwt:secret');
            $this->fixes[] = '运行 php artisan jwt:secret 生成JWT密钥';
        }
        
        // 检查生产环境配置
        if (isset($env['APP_ENV']) && $env['APP_ENV'] === 'production') {
            if (isset($env['APP_DEBUG']) && $env['APP_DEBUG'] !== 'false') {
                $this->issues[] = '生产环境应关闭调试模式';
                $this->log('WARN', '生产环境建议设置 APP_DEBUG=false');
                $this->fixes[] = '设置 APP_DEBUG=false';
            }
            
            if (isset($env['LOG_LEVEL']) && in_array($env['LOG_LEVEL'], ['debug', 'info'])) {
                $this->issues[] = '生产环境日志级别过低';
                $this->log('WARN', '生产环境建议设置 LOG_LEVEL=error');
                $this->fixes[] = '设置 LOG_LEVEL=error';
            }
        }
        
        // 检查数据库配置
        if (isset($env['DB_PASSWORD']) && empty($env['DB_PASSWORD'])) {
            $this->issues[] = '数据库密码为空，可能存在安全风险';
            $this->log('WARN', '数据库密码为空，建议设置强密码');
        }
        
        // 检查域名配置
        if (isset($env['APP_URL']) && (
            $env['APP_URL'] === 'http://localhost' || 
            $env['APP_URL'] === 'http://localhost:8000' ||
            strpos($env['APP_URL'], 'your-domain.com') !== false
        )) {
            $this->issues[] = 'APP_URL使用默认值，需要修改为实际域名';
            $this->log('WARN', 'APP_URL需要修改为实际域名');
            $this->fixes[] = '修改APP_URL为实际域名';
        }
    }
    
    /**
     * 自动修复配置问题
     */
    public function autoFixConfig()
    {
        $this->log('CHECK', '尝试自动修复配置问题...');
        
        $envFile = $this->projectRoot . '/.env';
        
        // 如果.env不存在，尝试从模板创建
        if (!file_exists($envFile)) {
            $templateFile = null;
            
            if (file_exists($this->projectRoot . '/.env.baota')) {
                $templateFile = $this->projectRoot . '/.env.baota';
                $this->log('INFO', '使用.env.baota模板创建.env文件');
            } elseif (file_exists($this->projectRoot . '/.env.example')) {
                $templateFile = $this->projectRoot . '/.env.example';
                $this->log('INFO', '使用.env.example模板创建.env文件');
            }
            
            if ($templateFile) {
                copy($templateFile, $envFile);
                $this->log('SUCCESS', '.env文件创建成功');
                $this->fixes[] = '创建了.env文件';
            }
        }
        
        // 生成APP_KEY
        if (file_exists($envFile)) {
            $env = $this->loadEnvFile($envFile);
            
            if (!isset($env['APP_KEY']) || empty($env['APP_KEY']) || $env['APP_KEY'] === 'base64:') {
                $this->log('INFO', '生成应用密钥...');
                exec('php artisan key:generate --force 2>&1', $output, $returnCode);
                
                if ($returnCode === 0) {
                    $this->log('SUCCESS', '应用密钥生成成功');
                    $this->fixes[] = '生成了应用密钥';
                } else {
                    $this->log('ERROR', '应用密钥生成失败');
                }
            }
            
            // 生成JWT_SECRET
            if (!isset($env['JWT_SECRET']) || empty($env['JWT_SECRET'])) {
                $this->log('INFO', '生成JWT密钥...');
                exec('php artisan jwt:secret --force 2>&1', $output, $returnCode);
                
                if ($returnCode === 0) {
                    $this->log('SUCCESS', 'JWT密钥生成成功');
                    $this->fixes[] = '生成了JWT密钥';
                } else {
                    $this->log('WARN', 'JWT密钥生成失败，可能需要手动设置');
                }
            }
        }
    }
    
    /**
     * 运行完整检查
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔍 环境配置检查和修复工具                         ║\n";
        echo "║                                                          ║\n";
        echo "║    检查.env文件完整性，确保宝塔部署成功                   ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 检查配置文件
        $configOk = $this->checkEnvFile();
        
        // 尝试自动修复
        if (!$configOk || !empty($this->issues)) {
            $this->autoFixConfig();
            
            // 重新检查
            $this->issues = [];
            $configOk = $this->checkEnvFile();
        }
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        echo "\n";
        echo "============================================================\n";
        echo "                    检查结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if (empty($this->issues)) {
            echo "🎉 环境配置检查通过！\n";
        } else {
            echo "⚠️  发现 " . count($this->issues) . " 个配置问题\n";
        }
        
        echo "\n";
        echo "📊 检查统计:\n";
        echo "   问题数量: " . count($this->issues) . " 个\n";
        echo "   修复数量: " . count($this->fixes) . " 个\n";
        echo "   检查耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->issues)) {
            echo "❌ 发现的问题:\n";
            foreach ($this->issues as $issue) {
                echo "   - {$issue}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        echo "🚀 下一步操作:\n";
        echo "   1. 检查.env文件配置: 修改域名、数据库等信息\n";
        echo "   2. 测试数据库连接: php artisan migrate:status\n";
        echo "   3. 运行部署脚本: bash 使用现有数据库部署.sh\n";
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 请根据实际情况修改.env文件中的配置项。\n";
        echo "\n";
        
        return empty($this->issues);
    }
}

// 运行检查工具
if (php_sapi_name() === 'cli') {
    $checker = new EnvConfigChecker();
    $success = $checker->run();
    exit($success ? 0 : 1);
}
