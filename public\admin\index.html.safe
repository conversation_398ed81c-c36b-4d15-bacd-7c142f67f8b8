<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">
    
    <!-- 使用传统的脚本加载方式，避免模块初始化问题 -->
    <script>
      // 防止模块加载错误的保护机制
      window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('Cannot access') && e.message.includes('before initialization')) {
          console.warn('检测到模块初始化错误，尝试重新加载...');
          
          // 清除缓存并重新加载
          if ('caches' in window) {
            caches.keys().then(function(names) {
              return Promise.all(names.map(name => caches.delete(name)));
            }).then(function() {
              setTimeout(() => location.reload(true), 1000);
            });
          } else {
            setTimeout(() => location.reload(true), 1000);
          }
        }
      });
      
      // 延迟加载主应用
      document.addEventListener('DOMContentLoaded', function() {
        // 确保DOM完全加载后再加载Vue应用
        setTimeout(function() {
          const script = document.createElement('script');
          script.type = 'module';
          script.crossOrigin = 'anonymous';
          script.src = '/admin/assets/index-BWOuGn0N.js';
          
          script.onerror = function() {
            console.error('主应用脚本加载失败');
            document.getElementById('loading-text').textContent = '加载失败，请刷新页面重试';
          };
          
          script.onload = function() {
            console.log('主应用脚本加载成功');
          };
          
          document.head.appendChild(script);
        }, 100);
      });
    </script>
  </head>
  <body>
    <div id="app">
      <!-- 简单的加载指示器 -->
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          background: white;
          padding: 40px;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          min-width: 300px;
        ">
          <div style="
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          "></div>
          
          <h3 style="
            color: #333;
            margin: 0 0 10px 0;
            font-size: 20px;
            font-weight: 600;
          ">晨鑫流量变现</h3>
          
          <p id="loading-text" style="
            color: #666;
            margin: 0;
            font-size: 16px;
          ">正在加载管理后台...</p>
          
          <div style="
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #999;
          ">
            如果长时间未加载，请尝试刷新页面
          </div>
        </div>
      </div>
      
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        body {
          margin: 0;
          padding: 0;
        }
      </style>
    </div>
  </body>
</html>
