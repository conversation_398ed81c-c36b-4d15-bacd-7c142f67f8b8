<?php

/**
 * 宝塔重新部署预检查工具
 * 确保环境和项目文件准备就绪
 */

class BaotaRedeployChecker
{
    private $projectRoot;
    private $checks = [];
    private $issues = [];
    private $fixes = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'CHECK' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 检查项目文件完整性
     */
    private function checkProjectFiles()
    {
        $this->log('CHECK', '检查项目文件完整性...');
        
        $requiredFiles = [
            '使用现有数据库部署.sh' => '主要部署脚本',
            '小白一键部署.sh' => '简化部署脚本',
            'auto_generate_env_simple.php' => '环境配置生成工具',
            'fix_current_deployment.php' => '部署修复工具',
            'fix_frontend_issues.php' => '前端修复工具',
            'fix_vue_errors.php' => 'Vue错误修复工具',
            'verify_deployment.php' => '部署验证工具',
            'composer.json' => 'Composer配置文件',
            'artisan' => 'Laravel命令行工具',
            'public/index.php' => 'Laravel入口文件'
        ];
        
        $allFilesExist = true;
        foreach ($requiredFiles as $file => $description) {
            $filePath = $this->projectRoot . '/' . $file;
            if (file_exists($filePath)) {
                $this->log('SUCCESS', "✅ {$description}: {$file}");
                $this->checks[] = "✅ {$description}";
            } else {
                $this->log('ERROR', "❌ {$description}缺失: {$file}");
                $this->issues[] = "{$description}缺失";
                $allFilesExist = false;
            }
        }
        
        return $allFilesExist;
    }
    
    /**
     * 检查前端项目结构
     */
    private function checkFrontendStructure()
    {
        $this->log('CHECK', '检查前端项目结构...');
        
        $frontendProjects = [
            'admin' => '管理后台',
            'frontend' => '前端应用',
            'distributor' => '分销后台'
        ];
        
        $allProjectsOk = true;
        foreach ($frontendProjects as $dir => $description) {
            $projectPath = $this->projectRoot . '/' . $dir;
            if (is_dir($projectPath)) {
                $this->log('SUCCESS', "✅ {$description}项目存在: {$dir}/");
                $this->checks[] = "✅ {$description}项目";
                
                // 检查关键文件
                $keyFiles = [];
                if ($dir === 'admin') {
                    $keyFiles = ['dist/index.html', 'dist/assets/'];
                } elseif ($dir === 'frontend') {
                    $keyFiles = ['.output/public/index.html'];
                } elseif ($dir === 'distributor') {
                    $keyFiles = ['index.html', 'login/index.html'];
                }
                
                foreach ($keyFiles as $file) {
                    $filePath = $projectPath . '/' . $file;
                    if (file_exists($filePath) || is_dir($filePath)) {
                        $this->log('SUCCESS', "  ✅ {$file}");
                    } else {
                        $this->log('WARN', "  ⚠️  {$file} 缺失");
                    }
                }
            } else {
                $this->log('ERROR', "❌ {$description}项目不存在: {$dir}/");
                $this->issues[] = "{$description}项目缺失";
                $allProjectsOk = false;
            }
        }
        
        return $allProjectsOk;
    }
    
    /**
     * 检查Laravel项目结构
     */
    private function checkLaravelStructure()
    {
        $this->log('CHECK', '检查Laravel项目结构...');
        
        $requiredDirs = [
            'app' => '应用程序目录',
            'config' => '配置文件目录',
            'database' => '数据库文件目录',
            'public' => '公共文件目录',
            'resources' => '资源文件目录',
            'routes' => '路由文件目录',
            'storage' => '存储目录',
            'vendor' => 'Composer依赖目录'
        ];
        
        $allDirsExist = true;
        foreach ($requiredDirs as $dir => $description) {
            $dirPath = $this->projectRoot . '/' . $dir;
            if (is_dir($dirPath)) {
                $this->log('SUCCESS', "✅ {$description}: {$dir}/");
                $this->checks[] = "✅ {$description}";
            } else {
                $this->log('ERROR', "❌ {$description}不存在: {$dir}/");
                $this->issues[] = "{$description}缺失";
                $allDirsExist = false;
            }
        }
        
        return $allDirsExist;
    }
    
    /**
     * 检查部署脚本权限
     */
    private function checkScriptPermissions()
    {
        $this->log('CHECK', '检查部署脚本权限...');
        
        $scripts = [
            '使用现有数据库部署.sh',
            '小白一键部署.sh'
        ];
        
        $allExecutable = true;
        foreach ($scripts as $script) {
            $scriptPath = $this->projectRoot . '/' . $script;
            if (file_exists($scriptPath)) {
                if (is_executable($scriptPath)) {
                    $this->log('SUCCESS', "✅ {$script} 可执行");
                    $this->checks[] = "✅ {$script}权限正确";
                } else {
                    $this->log('WARN', "⚠️  {$script} 不可执行，尝试修复...");
                    if (chmod($scriptPath, 0755)) {
                        $this->log('SUCCESS', "修复 {$script} 权限成功");
                        $this->fixes[] = "修复{$script}执行权限";
                        $this->checks[] = "✅ {$script}权限已修复";
                    } else {
                        $this->log('ERROR', "修复 {$script} 权限失败");
                        $this->issues[] = "{$script}权限错误";
                        $allExecutable = false;
                    }
                }
            }
        }
        
        return $allExecutable;
    }
    
    /**
     * 清理旧的部署文件
     */
    private function cleanupOldFiles()
    {
        $this->log('CHECK', '清理旧的部署文件...');
        
        $filesToClean = [
            '.env',
            '.env.backup.*',
            'storage/logs/laravel.log',
            'bootstrap/cache/config.php',
            'bootstrap/cache/routes-v7.php',
            'bootstrap/cache/services.php'
        ];
        
        $cleanedFiles = 0;
        foreach ($filesToClean as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // 处理通配符
                $files = glob($this->projectRoot . '/' . $pattern);
                foreach ($files as $file) {
                    if (unlink($file)) {
                        $cleanedFiles++;
                        $this->log('SUCCESS', "清理文件: " . basename($file));
                    }
                }
            } else {
                $filePath = $this->projectRoot . '/' . $pattern;
                if (file_exists($filePath)) {
                    if (unlink($filePath)) {
                        $cleanedFiles++;
                        $this->log('SUCCESS', "清理文件: {$pattern}");
                    }
                }
            }
        }
        
        if ($cleanedFiles > 0) {
            $this->fixes[] = "清理了{$cleanedFiles}个旧文件";
        }
        
        $this->log('SUCCESS', "清理完成，共清理 {$cleanedFiles} 个文件");
        return true;
    }
    
    /**
     * 创建必要的目录
     */
    private function createRequiredDirectories()
    {
        $this->log('CHECK', '创建必要的目录...');
        
        $requiredDirs = [
            'storage/logs',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'bootstrap/cache'
        ];
        
        $createdDirs = 0;
        foreach ($requiredDirs as $dir) {
            $dirPath = $this->projectRoot . '/' . $dir;
            if (!is_dir($dirPath)) {
                if (mkdir($dirPath, 0755, true)) {
                    $createdDirs++;
                    $this->log('SUCCESS', "创建目录: {$dir}");
                } else {
                    $this->log('ERROR', "创建目录失败: {$dir}");
                    $this->issues[] = "无法创建目录: {$dir}";
                }
            }
        }
        
        if ($createdDirs > 0) {
            $this->fixes[] = "创建了{$createdDirs}个必要目录";
        }
        
        return true;
    }
    
    /**
     * 生成部署信息摘要
     */
    private function generateDeploymentSummary()
    {
        $this->log('INFO', '生成部署信息摘要...');
        
        $summary = [
            '项目名称' => '晨鑫流量变现系统',
            '项目版本' => 'v2.1.0',
            '部署时间' => date('Y-m-d H:i:s'),
            '项目路径' => $this->projectRoot,
            '前端项目' => 'admin, frontend, distributor',
            '部署脚本' => '使用现有数据库部署.sh (推荐)',
            '预期部署时间' => '10-15分钟',
            '预期成功率' => '98%+'
        ];
        
        echo "\n";
        echo "============================================================\n";
        echo "                    部署信息摘要\n";
        echo "============================================================\n";
        foreach ($summary as $key => $value) {
            echo sprintf("%-15s: %s\n", $key, $value);
        }
        echo "\n";
    }
    
    /**
     * 运行预检查流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔍 宝塔重新部署预检查工具                         ║\n";
        echo "║                                                          ║\n";
        echo "║    确保环境和项目文件准备就绪                             ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 执行所有检查
        $filesOk = $this->checkProjectFiles();
        $frontendOk = $this->checkFrontendStructure();
        $laravelOk = $this->checkLaravelStructure();
        $scriptsOk = $this->checkScriptPermissions();
        
        // 执行清理和准备
        $this->cleanupOldFiles();
        $this->createRequiredDirectories();
        
        $allOk = $filesOk && $frontendOk && $laravelOk && $scriptsOk;
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 显示检查结果
        echo "\n";
        echo "============================================================\n";
        echo "                    预检查结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if ($allOk && empty($this->issues)) {
            echo "🎉 预检查全部通过！项目已准备就绪！\n";
        } else {
            echo "⚠️  预检查完成，发现 " . count($this->issues) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 检查统计:\n";
        echo "   检查项目: " . count($this->checks) . " 项\n";
        echo "   发现问题: " . count($this->issues) . " 个\n";
        echo "   自动修复: " . count($this->fixes) . " 个\n";
        echo "   检查耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->issues)) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->issues as $issue) {
                echo "   - {$issue}\n";
            }
            echo "\n";
        }
        
        echo "✅ 通过的检查:\n";
        foreach ($this->checks as $check) {
            echo "   {$check}\n";
        }
        echo "\n";
        
        // 生成部署信息摘要
        $this->generateDeploymentSummary();
        
        echo "🚀 下一步操作:\n";
        if ($allOk && empty($this->issues)) {
            echo "   1. 上传项目到宝塔网站目录\n";
            echo "   2. 设置网站根目录为 public 目录\n";
            echo "   3. 运行部署脚本: bash 使用现有数据库部署.sh\n";
            echo "   4. 按提示输入配置信息\n";
            echo "   5. 等待部署完成\n";
        } else {
            echo "   1. 解决上述问题\n";
            echo "   2. 重新运行预检查\n";
            echo "   3. 确认所有检查通过后再部署\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 确保宝塔面板已安装PHP8.1+、MySQL5.7+和Nginx。\n";
        echo "\n";
        
        return $allOk && empty($this->issues);
    }
}

// 运行预检查工具
if (php_sapi_name() === 'cli') {
    $checker = new BaotaRedeployChecker();
    $success = $checker->run();
    exit($success ? 0 : 1);
}
