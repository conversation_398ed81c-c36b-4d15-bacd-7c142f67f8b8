<?php

/**
 * 部署验证工具
 * 验证所有修复是否生效，确保系统可以正常运行
 */

class DeploymentVerifier
{
    private $projectRoot;
    private $checks = [];
    private $issues = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'CHECK' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 验证.env文件配置
     */
    private function verifyEnvConfig()
    {
        $this->log('CHECK', '验证.env文件配置...');
        
        $envFile = $this->projectRoot . '/.env';
        if (!file_exists($envFile)) {
            $this->log('ERROR', '.env文件不存在');
            $this->issues[] = '.env文件缺失';
            return false;
        }
        
        $content = file_get_contents($envFile);
        $config = [];
        
        foreach (explode("\n", $content) as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) continue;
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $config[trim($key)] = trim($value, '"\'');
            }
        }
        
        // 检查关键配置
        $requiredConfigs = [
            'APP_URL' => 'https://f.fcwan.cn',
            'API_DOMAIN' => 'f.fcwan.cn',
            'DB_HOST' => '127.0.0.1',
            'DB_DATABASE' => 'ffjq',
            'DB_USERNAME' => 'ffjq'
        ];
        
        $allCorrect = true;
        foreach ($requiredConfigs as $key => $expectedValue) {
            if (isset($config[$key]) && $config[$key] === $expectedValue) {
                $this->log('SUCCESS', "✅ {$key} = {$expectedValue}");
                $this->checks[] = "✅ {$key}配置正确";
            } else {
                $actualValue = $config[$key] ?? '未设置';
                $this->log('ERROR', "❌ {$key} = {$actualValue} (期望: {$expectedValue})");
                $this->issues[] = "{$key}配置错误";
                $allCorrect = false;
            }
        }
        
        // 检查密钥
        if (!empty($config['APP_KEY']) && strlen($config['APP_KEY']) > 20) {
            $this->log('SUCCESS', '✅ APP_KEY已设置');
            $this->checks[] = '✅ APP_KEY已设置';
        } else {
            $this->log('ERROR', '❌ APP_KEY未设置或无效');
            $this->issues[] = 'APP_KEY未设置';
            $allCorrect = false;
        }
        
        if (!empty($config['JWT_SECRET']) && strlen($config['JWT_SECRET']) > 20) {
            $this->log('SUCCESS', '✅ JWT_SECRET已设置');
            $this->checks[] = '✅ JWT_SECRET已设置';
        } else {
            $this->log('ERROR', '❌ JWT_SECRET未设置或无效');
            $this->issues[] = 'JWT_SECRET未设置';
            $allCorrect = false;
        }
        
        return $allCorrect;
    }
    
    /**
     * 验证数据库连接
     */
    private function verifyDatabaseConnection()
    {
        $this->log('CHECK', '验证数据库连接...');
        
        try {
            // 读取.env配置
            $envFile = $this->projectRoot . '/.env';
            $content = file_get_contents($envFile);
            $config = [];
            
            foreach (explode("\n", $content) as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '#') === 0) continue;
                
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $config[trim($key)] = trim($value, '"\'');
                }
            }
            
            $host = $config['DB_HOST'] ?? '127.0.0.1';
            $port = $config['DB_PORT'] ?? '3306';
            $database = $config['DB_DATABASE'] ?? '';
            $username = $config['DB_USERNAME'] ?? '';
            $password = $config['DB_PASSWORD'] ?? '';
            
            $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5,
            ]);
            
            // 检查数据库是否存在
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$database]);
            
            if ($stmt->rowCount() > 0) {
                $this->log('SUCCESS', '✅ 数据库连接成功');
                $this->checks[] = '✅ 数据库连接正常';
                
                // 检查关键表是否存在
                $pdo->exec("USE `{$database}`");
                $tables = ['users', 'migrations', 'wechat_groups'];
                foreach ($tables as $table) {
                    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$table]);
                    if ($stmt->rowCount() > 0) {
                        $this->log('SUCCESS', "✅ 表 {$table} 存在");
                        $this->checks[] = "✅ 表 {$table} 存在";
                    } else {
                        $this->log('WARN', "⚠️  表 {$table} 不存在");
                    }
                }
                
                return true;
            } else {
                $this->log('ERROR', "❌ 数据库 {$database} 不存在");
                $this->issues[] = "数据库 {$database} 不存在";
                return false;
            }
        } catch (PDOException $e) {
            $this->log('ERROR', '❌ 数据库连接失败: ' . $e->getMessage());
            $this->issues[] = '数据库连接失败: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 验证前端资源
     */
    private function verifyFrontendResources()
    {
        $this->log('CHECK', '验证前端资源...');
        
        $frontendDirs = ['admin', 'frontend', 'distributor'];
        $allOk = true;
        
        foreach ($frontendDirs as $dir) {
            $publicPath = $this->projectRoot . '/public/' . $dir;
            $indexFile = $publicPath . '/index.html';
            
            if (is_dir($publicPath)) {
                $this->log('SUCCESS', "✅ public/{$dir}/ 目录存在");
                $this->checks[] = "✅ {$dir}前端目录存在";
                
                if (file_exists($indexFile)) {
                    $this->log('SUCCESS', "✅ {$dir}/index.html 存在");
                    $this->checks[] = "✅ {$dir}入口文件存在";
                } else {
                    $this->log('ERROR', "❌ {$dir}/index.html 不存在");
                    $this->issues[] = "{$dir}入口文件缺失";
                    $allOk = false;
                }
            } else {
                $this->log('ERROR', "❌ public/{$dir}/ 目录不存在");
                $this->issues[] = "{$dir}前端目录缺失";
                $allOk = false;
            }
        }
        
        // 检查Vue错误处理脚本
        $errorHandlerScript = $this->projectRoot . '/public/admin/vue-error-handler.js';
        if (file_exists($errorHandlerScript)) {
            $this->log('SUCCESS', '✅ Vue错误处理脚本存在');
            $this->checks[] = '✅ Vue错误处理脚本';
        } else {
            $this->log('WARN', '⚠️  Vue错误处理脚本不存在');
        }
        
        return $allOk;
    }
    
    /**
     * 验证Laravel配置
     */
    private function verifyLaravelConfig()
    {
        $this->log('CHECK', '验证Laravel配置...');
        
        // 检查关键目录权限
        $writableDirs = [
            'storage/logs',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'bootstrap/cache'
        ];
        
        $allWritable = true;
        foreach ($writableDirs as $dir) {
            $path = $this->projectRoot . '/' . $dir;
            if (is_dir($path) && is_writable($path)) {
                $this->log('SUCCESS', "✅ {$dir} 可写");
                $this->checks[] = "✅ {$dir}权限正确";
            } else {
                $this->log('ERROR', "❌ {$dir} 不可写或不存在");
                $this->issues[] = "{$dir}权限错误";
                $allWritable = false;
            }
        }
        
        // 检查存储链接
        $storageLink = $this->projectRoot . '/public/storage';
        if (is_link($storageLink) || is_dir($storageLink)) {
            $this->log('SUCCESS', '✅ 存储链接存在');
            $this->checks[] = '✅ 存储链接正常';
        } else {
            $this->log('WARN', '⚠️  存储链接不存在');
        }
        
        return $allWritable;
    }
    
    /**
     * 验证Web服务器配置
     */
    private function verifyWebServerConfig()
    {
        $this->log('CHECK', '验证Web服务器配置...');
        
        // 检查.htaccess
        $htaccessFile = $this->projectRoot . '/public/.htaccess';
        if (file_exists($htaccessFile)) {
            $content = file_get_contents($htaccessFile);
            $this->log('SUCCESS', '✅ .htaccess文件存在');
            $this->checks[] = '✅ .htaccess文件存在';
            
            // 检查是否有缓存控制规则
            if (strpos($content, 'Cache-Control') !== false) {
                $this->log('SUCCESS', '✅ 缓存控制规则已添加');
                $this->checks[] = '✅ 缓存控制规则';
            } else {
                $this->log('WARN', '⚠️  缺少缓存控制规则');
            }
        } else {
            $this->log('ERROR', '❌ .htaccess文件不存在');
            $this->issues[] = '.htaccess文件缺失';
        }
        
        // 检查入口文件
        $indexFile = $this->projectRoot . '/public/index.php';
        if (file_exists($indexFile)) {
            $this->log('SUCCESS', '✅ Laravel入口文件存在');
            $this->checks[] = '✅ Laravel入口文件';
        } else {
            $this->log('ERROR', '❌ Laravel入口文件不存在');
            $this->issues[] = 'Laravel入口文件缺失';
        }
        
        return true;
    }
    
    /**
     * 运行验证流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         ✅ 部署验证工具                                   ║\n";
        echo "║                                                          ║\n";
        echo "║    验证所有修复是否生效，确保系统可以正常运行             ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 执行所有验证
        $envOk = $this->verifyEnvConfig();
        $dbOk = $this->verifyDatabaseConnection();
        $frontendOk = $this->verifyFrontendResources();
        $laravelOk = $this->verifyLaravelConfig();
        $webServerOk = $this->verifyWebServerConfig();
        
        $allOk = $envOk && $dbOk && $frontendOk && $laravelOk && $webServerOk;
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 显示验证结果
        echo "\n";
        echo "============================================================\n";
        echo "                    验证结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if ($allOk && empty($this->issues)) {
            echo "🎉 所有验证通过！系统已准备就绪！\n";
        } else {
            echo "⚠️  验证完成，发现 " . count($this->issues) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 验证统计:\n";
        echo "   检查项目: " . count($this->checks) . " 项\n";
        echo "   发现问题: " . count($this->issues) . " 个\n";
        echo "   验证耗时: {$duration}秒\n";
        echo "\n";
        
        echo "✅ 通过的检查:\n";
        foreach ($this->checks as $check) {
            echo "   {$check}\n";
        }
        echo "\n";
        
        if (!empty($this->issues)) {
            echo "❌ 需要注意的问题:\n";
            foreach ($this->issues as $issue) {
                echo "   - {$issue}\n";
            }
            echo "\n";
        }
        
        echo "🚀 访问地址:\n";
        echo "   前台首页: https://f.fcwan.cn\n";
        echo "   管理后台: https://f.fcwan.cn/admin\n";
        echo "   分销后台: https://f.fcwan.cn/distributor\n";
        echo "\n";
        
        if ($allOk && empty($this->issues)) {
            echo "🎊 恭喜！系统部署成功，可以正常使用了！\n";
        } else {
            echo "💡 建议解决上述问题后再进行使用。\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "📝 提示: 如果管理后台仍然空白，请强制刷新浏览器 (Ctrl+F5)。\n";
        echo "\n";
        
        return $allOk && empty($this->issues);
    }
}

// 运行验证工具
if (php_sapi_name() === 'cli') {
    $verifier = new DeploymentVerifier();
    $success = $verifier->run();
    exit($success ? 0 : 1);
}
