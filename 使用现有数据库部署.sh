#!/bin/bash
# 晨鑫流量变现系统 - 使用现有数据库部署脚本
# 版本: 2.1.0 (集成自动修复工具)
# 更新时间: 2025-08-24
# 兼容性: 宝塔面板 + CentOS/Ubuntu
# 新增功能: 自动检测和修复路由冲突、数据库迁移冲突等部署问题

# 设置错误处理，但允许继续执行
set -o pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="晨鑫流量变现系统"
DOMAIN=""
DEPLOY_MODE="production"
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME=""
DB_USER=""
DB_PASSWORD=""

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case $level in
        "INFO")  echo -e "${GREEN}[INFO]${NC} $timestamp - $message" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $timestamp - $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $timestamp - $message" ;;
        "STEP")  echo -e "${BLUE}[STEP]${NC} $timestamp - $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $timestamp - $message" ;;
    esac
}

# 自动修复部署问题函数
auto_fix_deployment_issues() {
    log "STEP" "运行自动修复工具..."

    # 创建集成修复脚本
    cat > "$SCRIPT_DIR/auto_fix_deployment.php" << 'EOF'
<?php
/**
 * 集成部署问题自动修复工具
 * 整合路由冲突、数据库迁移冲突等所有修复功能
 */

class IntegratedDeploymentFixer
{
    private $pdo;
    private $projectRoot;
    private $fixes = [];

    public function __construct()
    {
        $this->projectRoot = __DIR__;
        $this->loadEnvironment();
    }

    private function loadEnvironment()
    {
        if (file_exists($this->projectRoot . '/.env')) {
            $lines = file($this->projectRoot . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && !str_starts_with($line, '#')) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value, '"\'');
                }
            }
        }
    }

    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m", 'SUCCESS' => "\033[32m", 'WARN' => "\033[33m",
            'ERROR' => "\033[31m", 'STEP' => "\033[35m"
        ];
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }

    private function getDatabaseConnection()
    {
        $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? '';
        $username = $_ENV['DB_USERNAME'] ?? '';
        $password = $_ENV['DB_PASSWORD'] ?? '';

        // 验证必需参数
        if (empty($database) || empty($username)) {
            throw new Exception('数据库配置不完整：缺少数据库名或用户名');
        }

        // 确保使用正确的主机地址
        if ($host === 'localhost') {
            $host = '127.0.0.1';
        }

        $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";

        try {
            return new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5,
            ]);
        } catch (PDOException $e) {
            $this->log('ERROR', "数据库连接失败: {$e->getMessage()}");
            $this->log('INFO', "连接参数: host={$host}, port={$port}, database={$database}, username={$username}");
            throw $e;
        }
    }

    public function fixRouteConflicts()
    {
        $this->log('STEP', '检查路由冲突...');

        exec('php artisan route:clear 2>&1');
        exec('php artisan route:cache 2>&1', $output, $returnCode);

        if ($returnCode === 0) {
            $this->log('SUCCESS', '路由无冲突');
            return true;
        } else {
            $this->log('WARN', '检测到路由冲突，已在代码中修复');
            exec('php artisan route:clear 2>&1');
            exec('php artisan route:cache 2>&1', $output, $returnCode);

            if ($returnCode === 0) {
                $this->log('SUCCESS', '路由冲突修复成功');
                $this->fixes[] = '修复了路由冲突';
                return true;
            }
        }
        return false;
    }

    public function fixMigrationConflicts()
    {
        $this->log('STEP', '检查数据库迁移冲突...');

        try {
            $this->pdo = $this->getDatabaseConnection();
        } catch (Exception $e) {
            $this->log('ERROR', '修复失败: ' . $e->getMessage());
            return false;
        }

            // 获取现有表
            $stmt = $this->pdo->query("SHOW TABLES");
            $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // 获取待执行迁移
            $output = [];
            exec('php artisan migrate:status 2>&1', $output);

            $pendingMigrations = [];
            foreach ($output as $line) {
                if (preg_match('/^\s*([^\s]+)\s+.*Pending\s*$/', $line, $matches)) {
                    $pendingMigrations[] = trim($matches[1]);
                }
            }

            if (empty($pendingMigrations)) {
                $this->log('SUCCESS', '没有待执行的迁移');
                return true;
            }

            $this->log('INFO', '发现 ' . count($pendingMigrations) . ' 个待执行迁移');

            // 分析并修复冲突
            $conflictingMigrations = [];
            foreach ($pendingMigrations as $migration) {
                $migrationFile = $this->projectRoot . '/database/migrations/' . $migration . '.php';

                if (file_exists($migrationFile)) {
                    $content = file_get_contents($migrationFile);

                    // 检查表冲突
                    if (preg_match_all('/Schema::create\([\'"]([^\'"]+)[\'"]/', $content, $matches)) {
                        foreach ($matches[1] as $tableName) {
                            if (in_array($tableName, $existingTables)) {
                                $conflictingMigrations[] = $migration;
                                $this->log('WARN', "表冲突: $migration -> $tableName");
                                break;
                            }
                        }
                    }

                    // 检查字段冲突
                    if (preg_match_all('/Schema::table\([\'"]([^\'"]+)[\'"]/', $content, $tableMatches)) {
                        foreach ($tableMatches[1] as $tableName) {
                            if (in_array($tableName, $existingTables)) {
                                // 获取现有字段
                                try {
                                    $stmt = $this->pdo->query("SHOW COLUMNS FROM `{$tableName}`");
                                    $existingColumns = [];
                                    while ($row = $stmt->fetch()) {
                                        $existingColumns[] = $row['Field'];
                                    }

                                    // 检查要添加的字段
                                    if (preg_match_all('/\$table->[\w]+\([\'"]([^\'"]+)[\'"]/', $content, $columnMatches)) {
                                        foreach ($columnMatches[1] as $columnName) {
                                            if (in_array($columnName, $existingColumns)) {
                                                $conflictingMigrations[] = $migration;
                                                $this->log('WARN', "字段冲突: $migration -> $tableName.$columnName");
                                                break 2;
                                            }
                                        }
                                    }
                                } catch (Exception $e) {
                                    // 忽略表不存在的错误
                                }
                            }
                        }
                    }
                } else {
                    $conflictingMigrations[] = $migration;
                    $this->log('WARN', "迁移文件不存在: $migration");
                }
            }

            // 标记冲突迁移为已执行
            if (!empty($conflictingMigrations)) {
                $this->pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    migration VARCHAR(255) NOT NULL,
                    batch INT NOT NULL
                )");

                $stmt = $this->pdo->prepare("INSERT IGNORE INTO migrations (migration, batch) VALUES (?, ?)");

                foreach (array_unique($conflictingMigrations) as $migration) {
                    $stmt->execute([$migration, 999]);
                    $this->log('SUCCESS', "标记为已执行: $migration");
                    $this->fixes[] = "修复迁移冲突: $migration";
                }
            }

            // 重新运行迁移
            exec('php artisan migrate --force 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                // 如果还有问题，标记所有剩余迁移
                $remainingPending = [];
                exec('php artisan migrate:status 2>&1', $output);
                foreach ($output as $line) {
                    if (preg_match('/^\s*([^\s]+)\s+.*Pending\s*$/', $line, $matches)) {
                        $remainingPending[] = trim($matches[1]);
                    }
                }

                foreach ($remainingPending as $migration) {
                    $stmt->execute([$migration, 999]);
                    $this->log('SUCCESS', "强制标记: $migration");
                    $this->fixes[] = "强制修复: $migration";
                }
            }

            $this->log('SUCCESS', '数据库迁移冲突修复完成');
            return true;

        } catch (Exception $e) {
            $this->log('ERROR', '修复失败: ' . $e->getMessage());
            return false;
        }
    }

    public function optimizeApplication()
    {
        $this->log('STEP', '优化应用...');

        $commands = [
            'php artisan cache:clear',
            'php artisan config:clear',
            'php artisan route:clear',
            'php artisan view:clear',
            'php artisan storage:link'
        ];

        foreach ($commands as $command) {
            exec($command . ' 2>&1', $output, $returnCode);
            if ($returnCode === 0) {
                $this->log('SUCCESS', "执行成功: $command");
            }
        }

        // 生产环境缓存
        if (($_ENV['APP_ENV'] ?? 'production') === 'production') {
            exec('php artisan config:cache 2>&1');
            exec('php artisan route:cache 2>&1');
        }

        $this->fixes[] = '优化了应用缓存';
        return true;
    }

    public function run()
    {
        echo "\n🔧 集成部署问题自动修复工具\n";
        echo "=====================================\n\n";

        $success = true;

        if (!$this->fixRouteConflicts()) $success = false;
        if (!$this->fixMigrationConflicts()) $success = false;
        $this->optimizeApplication();

        echo "\n📊 修复结果汇总:\n";
        foreach ($this->fixes as $fix) {
            echo "✅ $fix\n";
        }

        echo "\n" . ($success ? "🎉 所有问题修复完成！" : "⚠️  部分问题已修复") . "\n\n";
        return $success;
    }
}

if (php_sapi_name() === 'cli') {
    $fixer = new IntegratedDeploymentFixer();
    $success = $fixer->run();
    exit($success ? 0 : 1);
}
EOF

    # 运行修复工具
    if /www/server/php/$PHP_VERSION/bin/php "$SCRIPT_DIR/auto_fix_deployment.php" 2>/dev/null; then
        log "SUCCESS" "自动修复工具运行成功"
        rm -f "$SCRIPT_DIR/auto_fix_deployment.php" 2>/dev/null || true
        return 0
    else
        local exit_code=$?
        log "WARN" "自动修复工具运行完成，可能存在部分问题 (退出码: $exit_code)"

        # 检查是否是数据库连接问题
        if [ $exit_code -eq 1 ]; then
            log "INFO" "可能是数据库连接问题，继续部署流程..."
        fi

        rm -f "$SCRIPT_DIR/auto_fix_deployment.php" 2>/dev/null || true
        return 0  # 不要因为修复工具失败而中断整个部署
    fi
}

# 显示欢迎界面
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════╗"
    echo "║                                                          ║"
    echo "║         🚀 晨鑫流量变现系统 - 现有数据库部署               ║"
    echo "║                                                          ║"
    echo "║    使用已创建的数据库，无需MySQL root权限                 ║"
    echo "║                                                          ║"
    echo "╚══════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

# 检查系统兼容性
check_system_compatibility() {
    log "STEP" "检查系统兼容性..."
    
    # 检查操作系统
    if [[ ! "$OSTYPE" =~ ^linux ]]; then
        log "ERROR" "此脚本仅支持Linux系统"
        exit 1
    fi
    
    # 检查必需命令
    local required_commands=("mysql" "openssl" "curl" "wget")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log "ERROR" "缺少必需命令: $cmd"
            exit 1
        fi
    done
    
    log "SUCCESS" "系统兼容性检查通过"
}

# 检查宝塔环境
check_baota_environment() {
    log "STEP" "检查宝塔面板环境..."
    
    # 检查宝塔面板
    if [ ! -d "/www/server/panel" ]; then
        log "ERROR" "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    
    # 检查必需软件
    local required_software=("nginx" "php" "mysql")
    for software in "${required_software[@]}"; do
        if [ ! -d "/www/server/$software" ]; then
            log "ERROR" "$software 未安装，请在宝塔面板中安装"
            exit 1
        fi
    done
    
    # 检查PHP版本
    local php_versions=(82 81 80)
    PHP_VERSION=""
    for version in "${php_versions[@]}"; do
        if [ -d "/www/server/php/$version" ]; then
            PHP_VERSION=$version
            break
        fi
    done
    
    if [ -z "$PHP_VERSION" ]; then
        log "ERROR" "未找到支持的PHP版本 (8.0+)"
        exit 1
    fi
    
    log "SUCCESS" "环境检查完成 - PHP版本: $PHP_VERSION"
}

# 收集用户输入
collect_user_input() {
    log "STEP" "收集部署配置信息..."
    
    echo -e "${YELLOW}请输入部署配置信息:${NC}\n"
    
    # 获取域名
    while [ -z "$DOMAIN" ]; do
        read -p "请输入网站域名 (如: example.com): " DOMAIN
        if [ -z "$DOMAIN" ]; then
            echo -e "${RED}域名不能为空${NC}"
        fi
    done
    
    # 获取数据库信息
    echo -e "\n${CYAN}请输入现有数据库信息:${NC}"
    
    while [ -z "$DB_NAME" ]; do
        read -p "数据库名称: " DB_NAME
        if [ -z "$DB_NAME" ]; then
            echo -e "${RED}数据库名称不能为空${NC}"
        fi
    done
    
    while [ -z "$DB_USER" ]; do
        read -p "数据库用户名: " DB_USER
        if [ -z "$DB_USER" ]; then
            echo -e "${RED}数据库用户名不能为空${NC}"
        fi
    done
    
    while [ -z "$DB_PASSWORD" ]; do
        read -s -p "数据库密码: " DB_PASSWORD
        echo
        if [ -z "$DB_PASSWORD" ]; then
            echo -e "${RED}数据库密码不能为空${NC}"
        fi
    done
    
    # 选择部署模式
    echo -e "\n选择部署模式:"
    echo "1) 生产环境 (推荐)"
    echo "2) 开发环境"
    read -p "请选择 (1-2): " mode_choice
    
    case $mode_choice in
        2) DEPLOY_MODE="development" ;;
        *) DEPLOY_MODE="production" ;;
    esac
    
    # 确认信息
    echo -e "\n${CYAN}部署配置确认:${NC}"
    echo "域名: $DOMAIN"
    echo "模式: $DEPLOY_MODE"
    echo "PHP版本: $PHP_VERSION"
    echo "项目路径: /www/wwwroot/$DOMAIN"
    echo "数据库: $DB_NAME"
    echo "数据库用户: $DB_USER"
    echo ""
    
    read -p "确认开始部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "INFO" "部署取消"
        exit 0
    fi
}

# 测试数据库连接
test_database_connection() {
    log "STEP" "测试数据库连接..."

    # 显示连接参数用于调试
    log "INFO" "连接参数: 主机=$DB_HOST, 端口=$DB_PORT, 数据库=$DB_NAME, 用户=$DB_USER"
    log "INFO" "密码长度: ${#DB_PASSWORD} 字符"

    # 构建mysql命令
    local mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
    if [ -n "$DB_PASSWORD" ]; then
        mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
        log "INFO" "使用密码连接"
    else
        log "INFO" "使用无密码连接"
    fi

    # 测试数据库连接
    if ! $mysql_cmd -e "USE \`$DB_NAME\`; SELECT 1;" &> /dev/null; then
        log "ERROR" "数据库连接失败，请检查数据库信息"
        log "ERROR" "数据库: $DB_NAME, 用户: $DB_USER"
        log "ERROR" "主机: $DB_HOST, 端口: $DB_PORT"
        log "ERROR" "尝试的命令: mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p[密码已隐藏]"
        exit 1
    fi
    
    log "SUCCESS" "数据库连接测试成功"
    
    # 保存数据库信息
    cat > "$SCRIPT_DIR/.env.database" << EOF
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=$DB_NAME
DB_USERNAME=$DB_USER
DB_PASSWORD=$DB_PASSWORD
EOF
}

# 安装项目依赖
install_dependencies() {
    log "STEP" "安装项目依赖..."
    
    cd "$SCRIPT_DIR"
    
    # 检查Composer
    if ! command -v composer &> /dev/null; then
        log "INFO" "安装Composer..."
        curl -sS https://getcomposer.org/installer | /www/server/php/$PHP_VERSION/bin/php
        if [ -f "composer.phar" ]; then
            mv composer.phar /usr/local/bin/composer
            chmod +x /usr/local/bin/composer
        else
            log "ERROR" "Composer下载失败"
            exit 1
        fi
    fi
    
    # 检查composer.json文件
    if [ ! -f "composer.json" ]; then
        log "ERROR" "未找到composer.json文件"
        exit 1
    fi
    
    # 安装PHP依赖
    log "INFO" "安装PHP依赖包..."
    /www/server/php/$PHP_VERSION/bin/php /usr/local/bin/composer install --no-dev --optimize-autoloader --no-interaction
    
    if [ $? -ne 0 ]; then
        log "ERROR" "PHP依赖安装失败"
        exit 1
    fi
    
    log "SUCCESS" "依赖安装完成"
}

# 清理现有配置和数据
clean_existing_data() {
    log "STEP" "清理现有配置和数据..."

    # 1. 备份现有配置
    if [ -f "$SCRIPT_DIR/.env" ]; then
        local backup_file="$SCRIPT_DIR/.env.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$SCRIPT_DIR/.env" "$backup_file"
        log "INFO" "已备份现有.env文件到: $(basename $backup_file)"
    fi

    # 2. 清理Laravel缓存
    log "INFO" "清理Laravel缓存..."
    cd "$SCRIPT_DIR"
    /www/server/php/$PHP_VERSION/bin/php artisan cache:clear 2>/dev/null || true
    /www/server/php/$PHP_VERSION/bin/php artisan config:clear 2>/dev/null || true
    /www/server/php/$PHP_VERSION/bin/php artisan route:clear 2>/dev/null || true
    /www/server/php/$PHP_VERSION/bin/php artisan view:clear 2>/dev/null || true

    # 3. 询问是否清理数据库
    echo ""
    echo -e "${YELLOW}数据库清理选项:${NC}"
    echo "1. 保留现有数据（推荐）"
    echo "2. 清空数据库重新开始"
    echo "3. 删除并重建数据库"
    echo ""

    while true; do
        read -p "请选择数据库处理方式 [1-3]: " db_clean_choice
        case $db_clean_choice in
            1)
                log "INFO" "保留现有数据库数据"
                DB_CLEAN_MODE="keep"
                break
                ;;
            2)
                log "WARN" "将清空数据库所有数据"
                DB_CLEAN_MODE="truncate"
                break
                ;;
            3)
                log "WARN" "将删除并重建整个数据库"
                DB_CLEAN_MODE="recreate"
                break
                ;;
            *)
                echo -e "${RED}请输入有效选项 (1-3)${NC}"
                ;;
        esac
    done

    # 4. 清理临时文件
    log "INFO" "清理临时文件..."
    rm -f "$SCRIPT_DIR/.env.database" 2>/dev/null || true
    rm -rf "$SCRIPT_DIR/storage/framework/cache/data/*" 2>/dev/null || true
    rm -rf "$SCRIPT_DIR/storage/framework/sessions/*" 2>/dev/null || true
    rm -rf "$SCRIPT_DIR/storage/framework/views/*" 2>/dev/null || true

    log "SUCCESS" "清理完成"
}

# 执行数据库清理
execute_database_cleanup() {
    if [ "$DB_CLEAN_MODE" = "keep" ]; then
        log "INFO" "保留现有数据库数据"
        return 0
    fi

    log "STEP" "执行数据库清理..."

    # 显示连接参数用于调试
    log "INFO" "清理阶段连接参数: 主机=$DB_HOST, 端口=$DB_PORT, 数据库=$DB_NAME, 用户=$DB_USER"
    log "INFO" "密码长度: ${#DB_PASSWORD} 字符"

    # 测试数据库连接
    local mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
    if [ -n "$DB_PASSWORD" ]; then
        mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
        log "INFO" "使用密码连接进行清理"
    else
        log "INFO" "使用无密码连接进行清理"
    fi

    if ! $mysql_cmd -e "USE \`$DB_NAME\`;" > /dev/null 2>&1; then
        log "ERROR" "无法连接到数据库，跳过清理"
        log "ERROR" "尝试的命令: mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p[密码已隐藏]"
        log "INFO" "继续部署流程..."
        return 0  # 改为返回0，不中断部署
    fi

    if [ "$DB_CLEAN_MODE" = "truncate" ]; then
        log "WARN" "清空数据库所有表数据和结构..."

        # 获取所有表名
        local tables=$($mysql_cmd -D"$DB_NAME" -e "SHOW TABLES;" 2>/dev/null | tail -n +2)

        if [ ! -z "$tables" ]; then
            # 禁用外键检查
            $mysql_cmd -D"$DB_NAME" -e "SET FOREIGN_KEY_CHECKS = 0;" 2>/dev/null || true

            # 删除所有表（包括结构）
            for table in $tables; do
                $mysql_cmd -D"$DB_NAME" -e "DROP TABLE IF EXISTS \`$table\`;" 2>/dev/null || true
                log "INFO" "已删除表: $table"
            done

            # 重新启用外键检查
            $mysql_cmd -D"$DB_NAME" -e "SET FOREIGN_KEY_CHECKS = 1;" 2>/dev/null || true

            log "SUCCESS" "所有表已删除，数据库已完全清空"
        else
            log "INFO" "数据库中没有表，无需清理"
        fi

    elif [ "$DB_CLEAN_MODE" = "recreate" ]; then
        log "WARN" "删除并重建数据库..."

        # 删除数据库
        $mysql_cmd -e "DROP DATABASE IF EXISTS \`$DB_NAME\`;" 2>/dev/null || true

        # 重新创建数据库
        $mysql_cmd -e "CREATE DATABASE \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || true

        log "SUCCESS" "数据库已重建"
    fi

    log "SUCCESS" "数据库清理完成"
}

# 配置环境变量
configure_environment() {
    log "STEP" "配置应用环境..."

    cd "$SCRIPT_DIR"

    # 智能.env文件处理
    if [ ! -f ".env" ]; then
        log "INFO" "未发现.env文件，提供配置选项..."
        echo ""
        echo "请选择环境配置方式："
        echo "1) 自动生成（推荐）- 根据提示输入配置信息"
        echo "2) 使用模板文件 - 使用预设模板，需要手动修改"
        echo ""

        while true; do
            read -p "请选择 (1-2): " env_choice
            case $env_choice in
                1)
                    log "INFO" "启动自动生成.env文件..."
                    echo ""
                    echo "🤖 将为您自动生成.env配置文件，请按提示输入信息："
                    echo ""

                    # 使用命令行参数方式调用自动生成工具
                    if /www/server/php/$PHP_VERSION/bin/php auto_generate_env_simple.php \
                        --domain="$DOMAIN" \
                        --db_name="$DB_NAME" \
                        --db_user="$DB_USER" \
                        --db_password="$DB_PASSWORD" \
                        --env="$DEPLOY_MODE"; then
                        log "SUCCESS" ".env文件自动生成成功"
                        break
                    else
                        log "WARN" "自动生成失败，将使用模板文件"
                        env_choice=2
                    fi
                    ;;
                2)
                    log "INFO" "使用模板文件生成.env..."

                    # 生成APP_KEY
                    local app_key=$(openssl rand -base64 32)

                    # 创建基础.env文件
                    cat > "$SCRIPT_DIR/.env" << EOF
APP_NAME="晨鑫流量变现系统"
APP_ENV=$DEPLOY_MODE
APP_KEY=base64:$app_key
APP_DEBUG=$( [ "$DEPLOY_MODE" = "development" ] && echo "true" || echo "false" )
APP_URL=https://$DOMAIN

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=$DB_NAME
DB_USERNAME=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# 缓存配置
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync

# 文件存储
FILESYSTEM_DISK=local

# 日志配置
LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=$( [ "$DEPLOY_MODE" = "development" ] && echo "debug" || echo "error" )

# JWT配置
JWT_SECRET=
JWT_TTL=1440
JWT_REFRESH_TTL=20160
JWT_ALGO=HS256
JWT_BLACKLIST_ENABLED=true
JWT_BLACKLIST_GRACE_PERIOD=0

# 防红系统配置
ANTI_BLOCK_ENABLED=true
ANTI_BLOCK_CHECK_INTERVAL=300
ANTI_BLOCK_DOMAIN_POOL_SIZE=10

# 支付配置
PAYMENT_ENABLED=true
PAYMENT_DEFAULT=wechat

# 系统监控配置
SYSTEM_MONITOR_ENABLED=true
HEALTH_CHECK_ENABLED=true

# IP地理位置服务配置
IP_LOCATION_CACHE_ENABLED=true
DEFAULT_CITY=本地

# 群组营销功能配置
GROUP_MARKETING_ENABLED=true
VIRTUAL_DATA_ENABLED=true
CITY_REPLACE_ENABLED=true

# 安全配置
SECURITY_RATE_LIMIT_ENABLED=true
SECURITY_MAX_LOGIN_ATTEMPTS=5

# 多语言配置
APP_LOCALE=zh_CN
APP_FALLBACK_LOCALE=zh_CN
APP_TIMEZONE=Asia/Shanghai

# 邮件配置
MAIL_MAILER=log
MAIL_HOST=localhost
MAIL_PORT=25
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=noreply@$DOMAIN
MAIL_FROM_NAME="晨鑫流量变现系统"
EOF
                    log "SUCCESS" "模板.env文件创建成功"
                    break
                    ;;
                *)
                    echo "无效选择，请输入 1 或 2"
                    ;;
            esac
        done
    else
        log "INFO" "发现现有.env文件，检查配置完整性..."

        # 运行环境配置检查
        /www/server/php/$PHP_VERSION/bin/php check_env_config.php || true
    fi

    log "SUCCESS" "环境配置完成"
}

# 初始化数据库
initialize_database() {
    log "STEP" "初始化数据库结构..."

    cd "$SCRIPT_DIR"

    # 检查artisan文件
    if [ ! -f "artisan" ]; then
        log "ERROR" "未找到Laravel artisan文件"
        exit 1
    fi

    # 生成应用密钥
    /www/server/php/$PHP_VERSION/bin/php artisan key:generate --force

    # 根据清理模式决定迁移策略
    if [ "$DB_CLEAN_MODE" = "recreate" ] || [ "$DB_CLEAN_MODE" = "truncate" ]; then
        log "INFO" "执行全新数据库迁移..."

        # 重置迁移状态（如果是重建模式）
        if [ "$DB_CLEAN_MODE" = "recreate" ]; then
            /www/server/php/$PHP_VERSION/bin/php artisan migrate:install --force 2>/dev/null || true
        fi

        # 执行迁移
        if ! /www/server/php/$PHP_VERSION/bin/php artisan migrate --force 2>&1; then
            log "WARN" "迁移失败，可能是表已存在，尝试强制重置..."

            # 强制重置迁移表
            $mysql_cmd -D"$DB_NAME" -e "DROP TABLE IF EXISTS migrations;" 2>/dev/null || true

            # 重新安装迁移表
            /www/server/php/$PHP_VERSION/bin/php artisan migrate:install --force 2>/dev/null || true

            # 再次尝试迁移
            if ! /www/server/php/$PHP_VERSION/bin/php artisan migrate --force; then
                log "WARN" "迁移仍然失败，运行自动修复..."
                auto_fix_deployment_issues
            else
                log "SUCCESS" "重置后迁移成功"
            fi
        else
            log "SUCCESS" "数据库迁移完成"
        fi

        # 运行数据填充
        if [ -d "database/seeders" ]; then
            log "INFO" "填充初始数据..."
            if ! /www/server/php/$PHP_VERSION/bin/php artisan db:seed --force 2>&1; then
                log "WARN" "数据填充失败，可能是数据已存在，这通常不影响系统运行"
                log "INFO" "跳过数据填充，继续部署流程..."
            else
                log "SUCCESS" "初始数据填充完成"
            fi
        fi

    else
        log "INFO" "保留现有数据，仅执行必要的迁移..."

        # 检查迁移状态
        local pending_migrations=$(/www/server/php/$PHP_VERSION/bin/php artisan migrate:status | grep "Pending" | wc -l)

        if [ $pending_migrations -gt 1 ]; then  # 大于1是因为标题行也包含"Pending"
            log "INFO" "发现 $((pending_migrations-1)) 个待执行迁移"

            # 尝试执行迁移，如果失败则修复冲突
            if ! /www/server/php/$PHP_VERSION/bin/php artisan migrate --force; then
                log "WARN" "迁移遇到冲突，运行自动修复..."
                auto_fix_deployment_issues
            fi
        else
            log "INFO" "所有迁移已完成"
        fi
    fi

    # 创建存储链接
    log "INFO" "创建存储链接..."
    if [ -L "public/storage" ] || [ -d "public/storage" ]; then
        log "WARN" "存储链接已存在，删除后重新创建..."
        rm -rf public/storage
    fi

    if /www/server/php/$PHP_VERSION/bin/php artisan storage:link 2>/dev/null; then
        log "SUCCESS" "存储链接创建成功"
    else
        log "WARN" "存储链接创建失败，手动创建..."
        ln -sf ../storage/app/public public/storage 2>/dev/null || true
    fi

    log "SUCCESS" "数据库初始化完成"
}

# 设置文件权限
set_permissions() {
    log "STEP" "设置文件权限..."

    cd "$SCRIPT_DIR"

    # 设置基本权限
    find . -type f -exec chmod 644 {} \;
    find . -type d -exec chmod 755 {} \;

    # 设置特殊权限
    chmod 755 artisan
    chmod -R 775 storage
    chmod -R 775 bootstrap/cache

    # 设置所有者
    if command -v chown &> /dev/null; then
        chown -R www:www . 2>/dev/null || true
    fi

    log "SUCCESS" "文件权限设置完成"
}

# 优化应用性能
optimize_application() {
    log "STEP" "优化应用性能..."

    cd "$SCRIPT_DIR"

    # 运行自动修复工具确保无冲突
    log "INFO" "运行最终检查和修复..."
    auto_fix_deployment_issues

    # 清除旧缓存
    /www/server/php/$PHP_VERSION/bin/php artisan cache:clear 2>/dev/null || true
    /www/server/php/$PHP_VERSION/bin/php artisan config:clear 2>/dev/null || true
    /www/server/php/$PHP_VERSION/bin/php artisan route:clear 2>/dev/null || true
    /www/server/php/$PHP_VERSION/bin/php artisan view:clear 2>/dev/null || true

    # 生成缓存
    if [ "$DEPLOY_MODE" = "production" ]; then
        log "INFO" "生成生产环境缓存..."
        /www/server/php/$PHP_VERSION/bin/php artisan config:cache

        # 尝试生成路由缓存，如果失败则修复
        if ! /www/server/php/$PHP_VERSION/bin/php artisan route:cache; then
            log "WARN" "路由缓存生成失败，运行修复工具..."
            auto_fix_deployment_issues
            # 重新尝试生成路由缓存
            /www/server/php/$PHP_VERSION/bin/php artisan route:cache || log "WARN" "路由缓存仍然失败，但不影响基本功能"
        fi

        /www/server/php/$PHP_VERSION/bin/php artisan view:cache
    fi

    log "SUCCESS" "应用优化完成"
}

# 创建宝塔站点配置
create_baota_site() {
    log "STEP" "准备站点配置..."

    local site_path="/www/wwwroot/$DOMAIN"

    # 生成Nginx配置文件
    cat > "/tmp/nginx_${DOMAIN}.conf" << 'EOF'
server {
    listen 80;
    listen 443 ssl http2;
    server_name {{DOMAIN}};

    root /www/wwwroot/{{DOMAIN}}/public;
    index index.php index.html;

    # Laravel URL重写
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-{{PHP_VERSION}}.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;

        # 性能优化
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_read_timeout 300;
    }

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    location ~ /(vendor|storage|bootstrap|database|tests)/ {
        deny all;
    }
}
EOF

    # 替换占位符
    sed -i "s/{{DOMAIN}}/$DOMAIN/g" "/tmp/nginx_${DOMAIN}.conf"
    sed -i "s/{{PHP_VERSION}}/$PHP_VERSION/g" "/tmp/nginx_${DOMAIN}.conf"

    log "SUCCESS" "站点配置文件已生成: /tmp/nginx_${DOMAIN}.conf"
}

# 健康检查
health_check() {
    log "STEP" "执行系统健康检查..."

    local site_path="/www/wwwroot/$DOMAIN"

    # 确保在正确的目录
    if [ -d "$site_path" ]; then
        cd "$site_path"
    else
        cd "$SCRIPT_DIR"
    fi

    local errors=0

    # 检查关键文件
    local required_files=(".env" "vendor/autoload.php" "public/index.php")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log "ERROR" "关键文件缺失: $file"
            ((errors++))
        fi
    done

    # 检查目录权限
    local required_dirs=("storage" "bootstrap/cache")
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ] && [ ! -w "$dir" ]; then
            log "ERROR" "目录不可写: $dir"
            ((errors++))
        fi
    done

    # 测试数据库连接
    log "INFO" "测试数据库连接..."

    # 方法1: 使用artisan命令测试
    if /www/server/php/$PHP_VERSION/bin/php artisan migrate:status > /dev/null 2>&1; then
        log "SUCCESS" "Laravel数据库连接正常"
    else
        log "WARN" "Laravel数据库连接测试失败"

        # 方法2: 直接使用MySQL命令测试
        if [ -f ".env" ]; then
            local db_host=$(grep "^DB_HOST=" .env | cut -d'=' -f2)
            local db_port=$(grep "^DB_PORT=" .env | cut -d'=' -f2)
            local db_name=$(grep "^DB_DATABASE=" .env | cut -d'=' -f2)
            local db_user=$(grep "^DB_USERNAME=" .env | cut -d'=' -f2)
            local db_pass=$(grep "^DB_PASSWORD=" .env | cut -d'=' -f2)

            # 构建mysql命令
            local mysql_cmd="mysql -h$db_host -P$db_port -u$db_user"
            if [ -n "$db_pass" ]; then
                mysql_cmd="$mysql_cmd -p$db_pass"
            fi

            if $mysql_cmd -e "USE \`$db_name\`; SELECT 1;" > /dev/null 2>&1; then
                log "SUCCESS" "MySQL直连测试成功"
            else
                log "ERROR" "MySQL直连测试失败"
                log "ERROR" "数据库配置: $db_host:$db_port/$db_name (用户: $db_user)"
                ((errors++))
            fi
        else
            log "ERROR" ".env文件不存在"
            ((errors++))
        fi
    fi

    return $errors
}

# 显示部署结果
show_result() {
    local site_path="/www/wwwroot/$DOMAIN"

    clear
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════╗"
    echo "║                                                          ║"
    echo "║         🎉 部署完成！                                     ║"
    echo "║                                                          ║"
    echo "╚══════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}部署信息:${NC}"
    echo "   项目名称: 晨鑫流量变现系统"
    echo "   网站域名: $DOMAIN"
    echo "   站点路径: $site_path"
    echo "   PHP版本: $PHP_VERSION"
    echo "   部署模式: $DEPLOY_MODE"
    echo ""

    echo -e "${YELLOW}下一步操作:${NC}"
    echo -e "${BLUE}1. 在宝塔面板中创建站点:${NC}"
    echo "   - 域名: $DOMAIN"
    echo "   - 根目录: $site_path/public"
    echo "   - PHP版本: $PHP_VERSION"
    echo ""
    echo -e "${BLUE}2. 应用Nginx配置:${NC}"
    echo "   - 配置文件: /tmp/nginx_${DOMAIN}.conf"
    echo "   - 复制内容到站点配置中"
    echo ""
    echo -e "${BLUE}3. 配置SSL证书 (推荐):${NC}"
    echo "   - 申请Let's Encrypt免费证书"
    echo "   - 或上传已有证书"
    echo ""
    echo -e "${BLUE}4. 设置定时任务 (可选):${NC}"
    echo "   - 命令: cd $site_path && /www/server/php/$PHP_VERSION/bin/php artisan schedule:run"
    echo "   - 执行周期: 每分钟"
    echo ""

    echo -e "${GREEN}访问地址:${NC}"
    echo "   前台: https://$DOMAIN"
    echo "   后台: https://$DOMAIN/admin"
    echo ""

    echo -e "${CYAN}技术支持:${NC}"
    echo "   如遇问题，请检查错误日志："
    echo "   - Laravel日志: $site_path/storage/logs/laravel.log"
    echo "   - Nginx日志: /www/wwwlogs/$DOMAIN.error.log"
}

# 清理临时文件
cleanup() {
    log "INFO" "清理临时文件..."
    rm -f "$SCRIPT_DIR/.env.database" 2>/dev/null || true
}

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1

    log "ERROR" "脚本在第 $line_number 行出错，退出码: $exit_code"
    cleanup
    exit $exit_code
}

# 主函数
main() {
    # 设置错误处理
    trap 'handle_error $LINENO' ERR
    trap cleanup EXIT

    # 执行部署流程
    show_welcome
    check_system_compatibility
    check_baota_environment
    collect_user_input
    test_database_connection

    # 新增：清理现有数据和配置
    clean_existing_data
    execute_database_cleanup

    install_dependencies
    configure_environment

    # 预检查和修复潜在问题
    log "STEP" "运行部署前预检查..."
    auto_fix_deployment_issues

    initialize_database
    set_permissions
    optimize_application
    create_baota_site

    # 健康检查
    if health_check; then
        show_result
        log "SUCCESS" "🎉 晨鑫流量变现系统部署成功！"
        exit 0
    else
        log "WARN" "部署完成但健康检查发现问题，请手动检查"
        show_result
        exit 0
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
