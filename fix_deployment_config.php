<?php

/**
 * 修复部署配置问题
 * 专门解决.env文件配置错误和数据库连接问题
 */

class DeploymentConfigFixer
{
    private $projectRoot;
    private $fixes = [];
    private $errors = [];
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
    }
    
    /**
     * 记录日志
     */
    private function log($level, $message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $colors = [
            'INFO' => "\033[36m",
            'SUCCESS' => "\033[32m",
            'WARN' => "\033[33m",
            'ERROR' => "\033[31m",
            'FIX' => "\033[35m"
        ];
        
        $color = $colors[$level] ?? "\033[0m";
        echo "[{$color}{$level}\033[0m] {$timestamp} - {$message}\n";
    }
    
    /**
     * 读取.env文件
     */
    private function readEnvFile()
    {
        $envFile = $this->projectRoot . '/.env';
        if (!file_exists($envFile)) {
            $this->log('ERROR', '.env文件不存在');
            return false;
        }
        
        $content = file_get_contents($envFile);
        $lines = explode("\n", $content);
        $config = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }
            
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $config[trim($key)] = trim($value, '"\'');
            }
        }
        
        return $config;
    }
    
    /**
     * 写入.env文件
     */
    private function writeEnvFile($config)
    {
        $envFile = $this->projectRoot . '/.env';
        
        // 备份现有文件
        if (file_exists($envFile)) {
            $backupFile = $envFile . '.backup.' . date('YmdHis');
            copy($envFile, $backupFile);
            $this->log('INFO', "已备份.env文件到: " . basename($backupFile));
        }
        
        $content = "# 修复后的环境配置文件\n";
        $content .= "# 修复时间: " . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($config as $key => $value) {
            // 如果值包含空格或特殊字符，用引号包围
            if (strpos($value, ' ') !== false || strpos($value, '#') !== false) {
                $value = '"' . $value . '"';
            }
            $content .= "{$key}={$value}\n";
        }
        
        if (file_put_contents($envFile, $content)) {
            $this->log('SUCCESS', '.env文件修复成功');
            return true;
        } else {
            $this->log('ERROR', '.env文件修复失败');
            return false;
        }
    }
    
    /**
     * 修复域名配置
     */
    private function fixDomainConfig(&$config)
    {
        $this->log('FIX', '修复域名配置...');
        
        $domain = 'f.fcwan.cn'; // 从部署日志中获取的正确域名
        
        // 修复APP_URL
        if (isset($config['APP_URL']) && $config['APP_URL'] !== "https://{$domain}") {
            $oldValue = $config['APP_URL'];
            $config['APP_URL'] = "https://{$domain}";
            $this->fixes[] = "修复APP_URL: {$oldValue} -> https://{$domain}";
            $this->log('SUCCESS', "修复APP_URL: https://{$domain}");
        }
        
        // 修复API_DOMAIN
        if (isset($config['API_DOMAIN']) && $config['API_DOMAIN'] !== $domain) {
            $config['API_DOMAIN'] = $domain;
            $this->fixes[] = "修复API_DOMAIN: {$domain}";
            $this->log('SUCCESS', "修复API_DOMAIN: {$domain}");
        }
        
        // 修复FRONTEND_URL
        if (isset($config['FRONTEND_URL']) && $config['FRONTEND_URL'] !== "https://{$domain}") {
            $config['FRONTEND_URL'] = "https://{$domain}";
            $this->fixes[] = "修复FRONTEND_URL: https://{$domain}";
            $this->log('SUCCESS', "修复FRONTEND_URL: https://{$domain}");
        }
        
        // 修复SESSION_DOMAIN
        if (isset($config['SESSION_DOMAIN']) && $config['SESSION_DOMAIN'] !== ".{$domain}") {
            $config['SESSION_DOMAIN'] = ".{$domain}";
            $this->fixes[] = "修复SESSION_DOMAIN: .{$domain}";
            $this->log('SUCCESS', "修复SESSION_DOMAIN: .{$domain}");
        }
    }
    
    /**
     * 修复数据库配置
     */
    private function fixDatabaseConfig(&$config)
    {
        $this->log('FIX', '修复数据库配置...');
        
        // 从部署日志中获取的正确数据库信息
        $dbConfig = [
            'DB_CONNECTION' => 'mysql',
            'DB_HOST' => '127.0.0.1',
            'DB_PORT' => '3306',
            'DB_DATABASE' => 'ffjq',
            'DB_USERNAME' => 'ffjq',
            'DB_PASSWORD' => '', // 需要用户提供
            'DB_CHARSET' => 'utf8mb4',
            'DB_COLLATION' => 'utf8mb4_unicode_ci',
            'DB_STRICT' => 'true',
            'DB_ENGINE' => 'InnoDB'
        ];
        
        foreach ($dbConfig as $key => $value) {
            if (!isset($config[$key]) || empty($config[$key]) || $config[$key] === 'n') {
                $config[$key] = $value;
                $this->fixes[] = "修复{$key}: {$value}";
                $this->log('SUCCESS', "修复{$key}: {$value}");
            }
        }
        
        // 检查数据库密码
        if (empty($config['DB_PASSWORD'])) {
            $this->log('WARN', 'DB_PASSWORD为空，请手动设置数据库密码');
            $this->errors[] = '数据库密码未设置，需要手动配置';
        }
    }
    
    /**
     * 修复应用名称
     */
    private function fixAppName(&$config)
    {
        $this->log('FIX', '修复应用名称...');
        
        // 修复APP_NAME
        if (isset($config['APP_NAME']) && $config['APP_NAME'] === 'f.fcwan.cn') {
            $config['APP_NAME'] = '晨鑫流量变现系统';
            $this->fixes[] = "修复APP_NAME: 晨鑫流量变现系统";
            $this->log('SUCCESS', "修复APP_NAME: 晨鑫流量变现系统");
        }
    }
    
    /**
     * 检查必需的密钥
     */
    private function checkRequiredKeys(&$config)
    {
        $this->log('FIX', '检查必需的密钥...');
        
        // 检查APP_KEY
        if (empty($config['APP_KEY']) || $config['APP_KEY'] === 'base64:') {
            $config['APP_KEY'] = 'base64:' . base64_encode(random_bytes(32));
            $this->fixes[] = "生成新的APP_KEY";
            $this->log('SUCCESS', "生成新的APP_KEY");
        }
        
        // 检查JWT_SECRET
        if (empty($config['JWT_SECRET'])) {
            $config['JWT_SECRET'] = bin2hex(random_bytes(32));
            $this->fixes[] = "生成新的JWT_SECRET";
            $this->log('SUCCESS', "生成新的JWT_SECRET");
        }
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection($config)
    {
        $this->log('INFO', '测试数据库连接...');
        
        try {
            $dsn = "mysql:host={$config['DB_HOST']};port={$config['DB_PORT']};dbname={$config['DB_DATABASE']};charset={$config['DB_CHARSET']}";
            $pdo = new PDO($dsn, $config['DB_USERNAME'], $config['DB_PASSWORD']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $this->log('SUCCESS', '数据库连接测试成功');
            return true;
        } catch (PDOException $e) {
            $this->log('ERROR', '数据库连接失败: ' . $e->getMessage());
            $this->errors[] = '数据库连接失败: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 运行Laravel命令
     */
    private function runLaravelCommands()
    {
        $this->log('INFO', '运行Laravel配置命令...');
        
        $commands = [
            'php artisan key:generate --force',
            'php artisan jwt:secret --force',
            'php artisan config:cache',
            'php artisan route:cache',
            'php artisan view:cache'
        ];
        
        foreach ($commands as $command) {
            $this->log('INFO', "执行: {$command}");
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->log('SUCCESS', "命令执行成功: {$command}");
                $this->fixes[] = "执行成功: {$command}";
            } else {
                $this->log('WARN', "命令执行失败: {$command}");
                $this->log('WARN', "输出: " . implode("\n", $output));
            }
        }
    }
    
    /**
     * 运行修复流程
     */
    public function run()
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════╗\n";
        echo "║                                                          ║\n";
        echo "║         🔧 部署配置修复工具                               ║\n";
        echo "║                                                          ║\n";
        echo "║    修复.env文件配置错误和数据库连接问题                   ║\n";
        echo "║                                                          ║\n";
        echo "╚══════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        $startTime = microtime(true);
        
        // 读取现有配置
        $config = $this->readEnvFile();
        if ($config === false) {
            return false;
        }
        
        $this->log('INFO', "读取到 " . count($config) . " 个配置项");
        
        // 执行修复
        $this->fixDomainConfig($config);
        $this->fixDatabaseConfig($config);
        $this->fixAppName($config);
        $this->checkRequiredKeys($config);
        
        // 写入修复后的配置
        if (!$this->writeEnvFile($config)) {
            return false;
        }
        
        // 测试数据库连接
        $dbConnected = $this->testDatabaseConnection($config);
        
        // 运行Laravel命令
        $this->runLaravelCommands();
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 显示修复结果
        echo "\n";
        echo "============================================================\n";
        echo "                    修复结果汇总\n";
        echo "============================================================\n";
        echo "\n";
        
        if (empty($this->errors)) {
            echo "🎉 配置修复完成！\n";
        } else {
            echo "⚠️  修复完成，但存在 " . count($this->errors) . " 个需要注意的问题\n";
        }
        
        echo "\n";
        echo "📊 修复统计:\n";
        echo "   修复项目: " . count($this->fixes) . " 个\n";
        echo "   发现问题: " . count($this->errors) . " 个\n";
        echo "   数据库连接: " . ($dbConnected ? "✅ 成功" : "❌ 失败") . "\n";
        echo "   修复耗时: {$duration}秒\n";
        echo "\n";
        
        if (!empty($this->fixes)) {
            echo "✅ 已执行的修复:\n";
            foreach ($this->fixes as $fix) {
                echo "   - {$fix}\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->errors as $error) {
                echo "   - {$error}\n";
            }
            echo "\n";
        }
        
        echo "🚀 下一步操作:\n";
        if ($dbConnected) {
            echo "   1. 运行数据库迁移: php artisan migrate --force\n";
            echo "   2. 填充初始数据: php artisan db:seed --force\n";
            echo "   3. 访问网站验证: https://f.fcwan.cn\n";
        } else {
            echo "   1. 检查数据库配置和密码\n";
            echo "   2. 重新运行修复工具\n";
            echo "   3. 确认数据库连接后继续部署\n";
        }
        echo "\n";
        echo "------------------------------------------------------------\n";
        echo "💡 提示: 如果数据库连接失败，请检查数据库密码设置。\n";
        echo "\n";
        
        return empty($this->errors);
    }
}

// 运行修复工具
if (php_sapi_name() === 'cli') {
    $fixer = new DeploymentConfigFixer();
    $success = $fixer->run();
    exit($success ? 0 : 1);
}
