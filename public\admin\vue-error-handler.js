// Vue.js错误处理脚本 - 修复版本
// 用于捕获和处理Vue初始化错误，但避免无限刷新

(function() {
    'use strict';

    // 防止无限刷新的计数器
    let refreshCount = parseInt(sessionStorage.getItem('vue_refresh_count') || '0');
    const MAX_REFRESH_COUNT = 3;

    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);

        // 检查是否是Vue相关错误
        if (event.error && event.error.message) {
            const message = event.error.message;

            if (message.includes('Cannot access') && message.includes('before initialization')) {
                console.warn('检测到模块初始化顺序错误:', message);

                // 检查是否是特定的 'xt' 变量错误
                if (message.includes('xt')) {
                    console.warn('检测到Vue/ElementPlus模块加载顺序问题');

                    // 防止无限刷新
                    if (refreshCount < MAX_REFRESH_COUNT) {
                        refreshCount++;
                        sessionStorage.setItem('vue_refresh_count', refreshCount.toString());

                        console.log(`尝试修复模块加载顺序 (${refreshCount}/${MAX_REFRESH_COUNT})...`);

                        // 清除可能的缓存
                        if ('serviceWorker' in navigator) {
                            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                                for(let registration of registrations) {
                                    registration.unregister();
                                }
                            });
                        }

                        // 延迟重新加载
                        setTimeout(function() {
                            window.location.reload(true);
                        }, 1000);
                    } else {
                        console.error('已达到最大刷新次数，停止自动刷新');
                        sessionStorage.removeItem('vue_refresh_count');

                        // 显示错误信息
                        const appElement = document.getElementById('app');
                        if (appElement) {
                            appElement.innerHTML = `
                                <div style="
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    height: 100vh;
                                    flex-direction: column;
                                    font-family: Arial, sans-serif;
                                    background: #f5f5f5;
                                ">
                                    <div style="
                                        background: white;
                                        padding: 40px;
                                        border-radius: 8px;
                                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                        text-align: center;
                                        max-width: 500px;
                                    ">
                                        <h2 style="color: #e74c3c; margin-bottom: 20px;">
                                            ⚠️ 系统加载异常
                                        </h2>
                                        <p style="color: #666; margin-bottom: 20px;">
                                            前端资源加载出现问题，请尝试以下解决方案：
                                        </p>
                                        <div style="text-align: left; margin-bottom: 20px;">
                                            <p>1. 清除浏览器缓存后刷新</p>
                                            <p>2. 使用 Ctrl+F5 强制刷新</p>
                                            <p>3. 尝试使用无痕模式访问</p>
                                        </div>
                                        <button onclick="sessionStorage.clear(); window.location.reload(true);" style="
                                            background: #3498db;
                                            color: white;
                                            border: none;
                                            padding: 10px 20px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            font-size: 14px;
                                            margin-right: 10px;
                                        ">
                                            清除缓存并刷新
                                        </button>
                                        <button onclick="window.location.href='/admin'" style="
                                            background: #95a5a6;
                                            color: white;
                                            border: none;
                                            padding: 10px 20px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            font-size: 14px;
                                        ">
                                            重新进入
                                        </button>
                                    </div>
                                </div>
                            `;
                        }
                    }
                }
            }
        }
    });

    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
    });

    // 页面加载成功后清除刷新计数器
    window.addEventListener('load', function() {
        // 延迟清除，确保Vue应用正常启动
        setTimeout(function() {
            if (document.getElementById('app').children.length > 0) {
                sessionStorage.removeItem('vue_refresh_count');
                console.log('Vue应用加载成功，清除刷新计数器');
            }
        }, 3000);
    });

    console.log('Vue错误处理脚本已加载 (防无限刷新版本)');
})();