// Vue.js模块加载错误处理脚本 - 智能修复版本
// 专门处理 'Cannot access before initialization' 错误

(function() {
    'use strict';

    // 防止无限刷新的计数器和时间窗口
    const STORAGE_KEY = 'vue_module_error_count';
    const TIME_WINDOW = 60000; // 1分钟时间窗口
    const MAX_RETRY_COUNT = 2;

    let errorData = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || '{"count": 0, "timestamp": 0}');
    const now = Date.now();

    // 如果超过时间窗口，重置计数器
    if (now - errorData.timestamp > TIME_WINDOW) {
        errorData = { count: 0, timestamp: now };
        sessionStorage.setItem(STORAGE_KEY, JSON.stringify(errorData));
    }

    // 专门处理模块初始化错误
    function handleModuleInitError(error, source) {
        const message = error.message || '';

        // 检查是否是模块初始化错误
        if (message.includes('Cannot access') && message.includes('before initialization')) {
            console.warn('🔧 检测到模块初始化错误:', message);
            console.warn('📍 错误来源:', source);

            // 检查是否在允许的重试次数内
            if (errorData.count < MAX_RETRY_COUNT) {
                errorData.count++;
                errorData.timestamp = now;
                sessionStorage.setItem(STORAGE_KEY, JSON.stringify(errorData));

                console.log(`🔄 尝试修复模块加载问题 (${errorData.count}/${MAX_RETRY_COUNT})`);

                // 使用更温和的修复方式
                setTimeout(function() {
                    // 清除模块缓存
                    if ('caches' in window) {
                        caches.keys().then(function(names) {
                            names.forEach(function(name) {
                                if (name.includes('admin') || name.includes('vue')) {
                                    caches.delete(name);
                                }
                            });
                        });
                    }

                    // 重新加载页面
                    window.location.reload(true);
                }, 500);

                return true; // 表示已处理
            } else {
                console.error('❌ 已达到最大重试次数，显示错误页面');
                showErrorPage('模块加载失败', '前端资源加载出现问题，请尝试清除浏览器缓存后重新访问');
                return true;
            }
        }

        return false; // 未处理
    }

    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('🚨 全局错误:', event.error);

        if (event.error) {
            const handled = handleModuleInitError(event.error, event.filename || 'unknown');
            if (handled) {
                event.preventDefault();
                return;
            }
        }

                        // 显示错误信息
                        const appElement = document.getElementById('app');
                        if (appElement) {
                            appElement.innerHTML = `
                                <div style="
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    height: 100vh;
                                    flex-direction: column;
                                    font-family: Arial, sans-serif;
                                    background: #f5f5f5;
                                ">
                                    <div style="
                                        background: white;
                                        padding: 40px;
                                        border-radius: 8px;
                                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                        text-align: center;
                                        max-width: 500px;
                                    ">
                                        <h2 style="color: #e74c3c; margin-bottom: 20px;">
                                            ⚠️ 系统加载异常
                                        </h2>
                                        <p style="color: #666; margin-bottom: 20px;">
                                            前端资源加载出现问题，请尝试以下解决方案：
                                        </p>
                                        <div style="text-align: left; margin-bottom: 20px;">
                                            <p>1. 清除浏览器缓存后刷新</p>
                                            <p>2. 使用 Ctrl+F5 强制刷新</p>
                                            <p>3. 尝试使用无痕模式访问</p>
                                        </div>
                                        <button onclick="sessionStorage.clear(); window.location.reload(true);" style="
                                            background: #3498db;
                                            color: white;
                                            border: none;
                                            padding: 10px 20px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            font-size: 14px;
                                            margin-right: 10px;
                                        ">
                                            清除缓存并刷新
                                        </button>
                                        <button onclick="window.location.href='/admin'" style="
                                            background: #95a5a6;
                                            color: white;
                                            border: none;
                                            padding: 10px 20px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            font-size: 14px;
                                        ">
                                            重新进入
                                        </button>
                                    </div>
                                </div>
                            `;
                        }
                    }
                }
            }
        }
    });

    // 显示错误页面
    function showErrorPage(title, message) {
        const appElement = document.getElementById('app');
        if (appElement) {
            appElement.innerHTML = `
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                ">
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 12px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                        max-width: 500px;
                        margin: 20px;
                    ">
                        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                        <h2 style="color: #e74c3c; margin-bottom: 16px; font-size: 24px;">${title}</h2>
                        <p style="color: #666; margin-bottom: 24px; line-height: 1.5;">${message}</p>
                        <div style="margin-bottom: 20px;">
                            <button onclick="location.reload(true)" style="
                                background: #409EFF;
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                margin-right: 10px;
                                transition: background 0.3s;
                            " onmouseover="this.style.background='#337ecc'" onmouseout="this.style.background='#409EFF'">
                                🔄 强制刷新
                            </button>
                            <button onclick="sessionStorage.clear(); localStorage.clear(); location.reload(true)" style="
                                background: #67C23A;
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                                transition: background 0.3s;
                            " onmouseover="this.style.background='#5daf34'" onmouseout="this.style.background='#67C23A'">
                                🧹 清除缓存
                            </button>
                        </div>
                        <div style="font-size: 12px; color: #999; border-top: 1px solid #eee; padding-top: 16px; margin-top: 16px;">
                            <p>如果问题持续存在，请尝试：</p>
                            <p>1. 使用 Ctrl+F5 强制刷新</p>
                            <p>2. 清除浏览器缓存和Cookie</p>
                            <p>3. 尝试使用无痕模式访问</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('🚨 未处理的Promise拒绝:', event.reason);

        // 检查是否是模块相关的Promise错误
        if (event.reason && event.reason.message) {
            const handled = handleModuleInitError(event.reason, 'Promise');
            if (handled) {
                event.preventDefault();
            }
        }
    });

    // 页面加载成功后清除错误计数器
    window.addEventListener('load', function() {
        setTimeout(function() {
            const appElement = document.getElementById('app');
            // 检查Vue应用是否成功挂载（有子元素且不是错误页面）
            if (appElement && appElement.children.length > 0 &&
                !appElement.innerHTML.includes('⚠️')) {
                sessionStorage.removeItem(STORAGE_KEY);
                console.log('✅ Vue应用加载成功，清除错误计数器');
            }
        }, 2000);
    });

    // 监听Vue应用挂载成功
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                const appElement = document.getElementById('app');
                if (appElement && appElement.children.length > 0 &&
                    !appElement.innerHTML.includes('正在加载管理后台') &&
                    !appElement.innerHTML.includes('⚠️')) {
                    sessionStorage.removeItem(STORAGE_KEY);
                    console.log('✅ Vue应用挂载成功，清除错误计数器');
                    observer.disconnect();
                }
            }
        });
    });

    // 开始观察DOM变化
    const appElement = document.getElementById('app');
    if (appElement) {
        observer.observe(appElement, { childList: true, subtree: true });
    }

    console.log('🛡️ Vue模块加载错误处理脚本已加载');
})();