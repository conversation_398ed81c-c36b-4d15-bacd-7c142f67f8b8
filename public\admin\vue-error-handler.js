// Vue.js错误处理脚本
// 用于捕获和处理Vue初始化错误

(function() {
    'use strict';
    
    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        
        // 检查是否是Vue相关错误
        if (event.error && event.error.message) {
            const message = event.error.message;
            
            if (message.includes('Cannot access') && message.includes('before initialization')) {
                console.warn('检测到模块初始化顺序错误:', message);

                // 检查是否是特定的 'xt' 变量错误
                if (message.includes('xt')) {
                    console.warn('检测到Vue/ElementPlus模块加载顺序问题，尝试修复...');

                    // 清除可能的缓存
                    if ('serviceWorker' in navigator) {
                        navigator.serviceWorker.getRegistrations().then(function(registrations) {
                            for(let registration of registrations) {
                                registration.unregister();
                            }
                        });
                    }

                    // 延迟重新加载，给模块时间正确初始化
                    setTimeout(function() {
                        console.log('重新加载页面以修复模块加载顺序...');
                        window.location.reload(true); // 强制从服务器重新加载
                    }, 1000);
                } else {
                    // 其他初始化错误
                    setTimeout(function() {
                        if (!window.Vue || !window.Vue.createApp) {
                            console.log('Vue未正确加载，刷新页面...');
                            window.location.reload();
                        }
                    }, 2000);
                }
            }
        }
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
    });
    
    // Vue特定错误处理
    document.addEventListener('DOMContentLoaded', function() {
        // 检查Vue是否正确加载
        setTimeout(function() {
            if (typeof window.Vue === 'undefined') {
                console.error('Vue未加载，可能存在脚本加载问题');
                
                // 显示错误信息给用户
                const appElement = document.getElementById('app');
                if (appElement && !appElement.innerHTML.trim()) {
                    appElement.innerHTML = `
                        <div style="
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            flex-direction: column;
                            font-family: Arial, sans-serif;
                            background: #f5f5f5;
                        ">
                            <div style="
                                background: white;
                                padding: 40px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                text-align: center;
                                max-width: 500px;
                            ">
                                <h2 style="color: #e74c3c; margin-bottom: 20px;">
                                    🔧 系统正在加载中...
                                </h2>
                                <p style="color: #666; margin-bottom: 20px;">
                                    前端资源正在初始化，请稍候片刻
                                </p>
                                <button onclick="window.location.reload()" style="
                                    background: #3498db;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    font-size: 14px;
                                ">
                                    刷新页面
                                </button>
                            </div>
                        </div>
                    `;
                }
            }
        }, 3000);
    });
    
    console.log('Vue错误处理脚本已加载');
})();