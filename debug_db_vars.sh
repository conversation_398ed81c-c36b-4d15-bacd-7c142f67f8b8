#!/bin/bash

# 数据库变量调试脚本
# 用于检查部署脚本中的数据库变量设置

echo "=== 数据库变量调试工具 ==="
echo ""

# 模拟部署脚本中的变量设置
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="ffjq"
DB_USER="ffjq"
DB_PASSWORD="your_password_here"  # 替换为实际密码

echo "1. 变量值检查:"
echo "   DB_HOST: '$DB_HOST'"
echo "   DB_PORT: '$DB_PORT'"
echo "   DB_NAME: '$DB_NAME'"
echo "   DB_USER: '$DB_USER'"
echo "   DB_PASSWORD: [${#DB_PASSWORD} 字符]"
echo ""

echo "2. 变量非空检查:"
[ -n "$DB_HOST" ] && echo "   ✅ DB_HOST 已设置" || echo "   ❌ DB_HOST 为空"
[ -n "$DB_PORT" ] && echo "   ✅ DB_PORT 已设置" || echo "   ❌ DB_PORT 为空"
[ -n "$DB_NAME" ] && echo "   ✅ DB_NAME 已设置" || echo "   ❌ DB_NAME 为空"
[ -n "$DB_USER" ] && echo "   ✅ DB_USER 已设置" || echo "   ❌ DB_USER 为空"
[ -n "$DB_PASSWORD" ] && echo "   ✅ DB_PASSWORD 已设置" || echo "   ❌ DB_PASSWORD 为空"
echo ""

echo "3. MySQL命令构建测试:"
mysql_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
if [ -n "$DB_PASSWORD" ]; then
    mysql_cmd="$mysql_cmd -p$DB_PASSWORD"
    echo "   构建的命令: mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p[密码已隐藏]"
    echo "   ✅ 包含密码参数"
else
    echo "   构建的命令: mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
    echo "   ⚠️  无密码参数"
fi
echo ""

echo "4. 连接测试 (不会实际连接):"
echo "   如果要测试连接，请运行:"
echo "   $mysql_cmd -e \"SELECT 1;\""
echo ""

echo "=== 调试完成 ==="
