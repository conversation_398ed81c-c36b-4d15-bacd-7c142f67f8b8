<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// 管理后台入口路由
Route::get('/admin/{path?}', function () {
    return response()->file(public_path('admin/index.html'));
})->where('path', '.*');

// 分销后台入口路由
Route::get('/distributor/{path?}', function () {
    return response()->file(public_path('distributor/index.html'));
})->where('path', '.*');

// 提现管理演示页面
Route::get('/withdrawal-demo', function () {
    return response()->file(public_path('withdrawal-demo.html'));
});

// 群组前端访问路由
Route::prefix('group')->name('group.')->group(function () {
    Route::get('{id}', [App\Http\Controllers\GroupController::class, 'index'])->name('index');
    Route::get('{id}/show', [App\Http\Controllers\GroupController::class, 'show'])->name('show');
    Route::get('{id}/share', [App\Http\Controllers\GroupShareController::class, 'share'])->name('share');
    Route::get('{id}/qrcode', [App\Http\Controllers\GroupController::class, 'qrcode'])->name('qrcode');
    Route::get('{id}/kefu', [App\Http\Controllers\GroupController::class, 'kefu'])->name('kefu');
    Route::get('{id}/success', [App\Http\Controllers\GroupController::class, 'success'])->name('web.success');
    
    // API接口
    Route::get('{id}/validate', [App\Http\Controllers\GroupController::class, 'validateAccess'])->name('validate');
    Route::get('{id}/stats', [App\Http\Controllers\GroupController::class, 'getAccessStats'])->name('stats');
    Route::get('{id}/geographic', [App\Http\Controllers\GroupController::class, 'getGeographicDistribution'])->name('geographic');
    
    // 支付相关
    Route::post('payment/create', [App\Http\Controllers\GroupShareController::class, 'createPayment'])->name('payment.create');
    Route::post('payment/query', [App\Http\Controllers\GroupShareController::class, 'queryOrderStatus'])->name('payment.query');
    
    // 防封系统路由
    Route::get('{id}/access', [App\Http\Controllers\GroupController::class, 'accessGroup'])->name('access');
    Route::get('{id}/redirect/{token}', [App\Http\Controllers\GroupController::class, 'handleRedirect'])->name('redirect');
    Route::get('{id}/guide', [App\Http\Controllers\GroupController::class, 'browserGuide'])->name('guide');
});

// 超级群组落地页路由（增强版）
Route::prefix('landing')->name('landing.')->group(function () {
    Route::get('group/{id}', [App\Http\Controllers\Api\UltraGroupController::class, 'showLandingPage'])->name('group');
    Route::get('ultra/{id}', [App\Http\Controllers\Api\UltraGroupController::class, 'showUltraLandingPage'])->name('ultra');
});

// 城市定位API路由
Route::prefix('api/location')->name('api.location.')->group(function () {
    Route::get('ip', [App\Http\Controllers\Api\LocationController::class, 'getLocationByIP'])->name('ip');
    Route::post('reverse', [App\Http\Controllers\Api\LocationController::class, 'reverseGeocode'])->name('reverse');
    Route::get('cities', [App\Http\Controllers\Api\LocationController::class, 'getCities'])->name('cities');
    Route::get('recommend', [App\Http\Controllers\Api\LocationController::class, 'recommendCities'])->name('recommend');
    Route::post('batch', [App\Http\Controllers\Api\LocationController::class, 'batchLocation'])->name('batch');
});

// 防红系统短链接路由
Route::prefix('s')->name('short.')->group(function () {
    // 基础短链接跳转
    Route::get('{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'redirect'])->name('redirect');
    
    // 混淆链接处理
    Route::get('o', [App\Http\Controllers\ShortLinkController::class, 'obfuscatedRedirect'])->name('obfuscated');
    
    // 加密链接处理
    Route::get('d', [App\Http\Controllers\ShortLinkController::class, 'decryptRedirect'])->name('decrypt');
    
    // 访问统计跟踪
    Route::get('t/{groupId}', [App\Http\Controllers\ShortLinkController::class, 'track'])->name('track');
});

// 防红系统API路由
Route::prefix('api/anti-block')->name('api.anti-block.')->group(function () {
    // 短链接信息
    Route::get('info/{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'info'])->name('info');
    
    // 短链接预览
    Route::get('preview/{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'preview'])->name('preview');
    
    // 二维码生成
    Route::get('qrcode/{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'qrcode'])->name('qrcode');
    
    // 批量检查
    Route::post('batch-check', [App\Http\Controllers\ShortLinkController::class, 'batchCheck'])->name('batch-check');
    
    // 热门链接
    Route::get('popular', [App\Http\Controllers\ShortLinkController::class, 'popular'])->name('popular');
    
    // 访问统计
    Route::get('stats/{shortCode}', [App\Http\Controllers\ShortLinkController::class, 'stats'])->name('stats');
    
    // 健康检查
    Route::get('health', [App\Http\Controllers\ShortLinkController::class, 'healthCheck'])->name('health');
});

// 错误页面路由
Route::get('error/{type}', function ($type) {
    $messages = [
        'link_expired' => '链接已失效',
        'system_error' => '系统错误',
        'access_denied' => '访问被拒绝',
    ];
    
    $message = $messages[$type] ?? '未知错误';
    
    return view('errors.custom', [
        'title' => '访问错误',
        'message' => $message,
        'type' => $type
    ]);
})->name('error.page');

// 引入其他路由文件
require __DIR__.'/admin_dashboard.php';
require __DIR__.'/admin_detail_views.php';
require __DIR__.'/admin_integrated_agents.php';
require __DIR__.'/admin_theme_api.php';
require __DIR__.'/admin_glassmorphism_demo.php';