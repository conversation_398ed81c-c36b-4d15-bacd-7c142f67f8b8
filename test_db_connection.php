<?php
/**
 * 数据库连接测试脚本
 * 用于排查数据库连接问题
 */

echo "=== 数据库连接测试 ===\n";

// 从 .env 文件读取配置
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("错误：.env 文件不存在\n");
}

$config = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
        list($key, $value) = explode('=', $line, 2);
        $config[trim($key)] = trim($value);
    }
}

$host = $config['DB_HOST'] ?? 'localhost';
$port = $config['DB_PORT'] ?? '3306';
$database = $config['DB_DATABASE'] ?? '';
$username = $config['DB_USERNAME'] ?? '';
$password = $config['DB_PASSWORD'] ?? '';

echo "配置信息：\n";
echo "主机: $host\n";
echo "端口: $port\n";
echo "数据库: $database\n";
echo "用户名: $username\n";
echo "密码长度: " . strlen($password) . " 字符\n";
echo "密码内容: " . ($password ? str_repeat('*', strlen($password)) : '(空)') . "\n\n";

// 测试不同的连接方式
$testConfigs = [
    ['host' => 'localhost', 'desc' => 'localhost'],
    ['host' => '127.0.0.1', 'desc' => '127.0.0.1'],
];

foreach ($testConfigs as $testConfig) {
    echo "测试连接: {$testConfig['desc']}\n";
    
    try {
        $dsn = "mysql:host={$testConfig['host']};port=$port;dbname=$database;charset=utf8mb4";
        echo "DSN: $dsn\n";
        
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "✅ 连接成功！\n";
        
        // 测试查询
        $stmt = $pdo->query("SELECT VERSION() as version");
        $result = $stmt->fetch();
        echo "MySQL 版本: " . $result['version'] . "\n";
        
        // 测试数据库访问
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll();
        echo "数据库中的表数量: " . count($tables) . "\n";
        
        if (count($tables) > 0) {
            echo "前5个表: ";
            for ($i = 0; $i < min(5, count($tables)); $i++) {
                echo array_values($tables[$i])[0] . " ";
            }
            echo "\n";
        }
        
        echo "数据库连接测试通过！\n\n";
        break; // 成功后退出循环
        
    } catch (PDOException $e) {
        echo "❌ 连接失败: " . $e->getMessage() . "\n";
        
        // 分析错误类型
        if (strpos($e->getMessage(), 'Access denied') !== false) {
            echo "   → 这是认证错误，请检查用户名和密码\n";
        } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
            echo "   → 这是连接被拒绝，请检查MySQL服务是否运行\n";
        } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
            echo "   → 数据库不存在，请检查数据库名称\n";
        }
        echo "\n";
    }
}

// 额外的诊断信息
echo "=== 诊断信息 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "PDO MySQL 扩展: " . (extension_loaded('pdo_mysql') ? '已安装' : '未安装') . "\n";

// 检查可能的密码问题
if (!empty($password)) {
    echo "密码分析：\n";
    echo "- 包含数字: " . (preg_match('/\d/', $password) ? '是' : '否') . "\n";
    echo "- 包含字母: " . (preg_match('/[a-zA-Z]/', $password) ? '是' : '否') . "\n";
    echo "- 包含特殊字符: " . (preg_match('/[^a-zA-Z0-9]/', $password) ? '是' : '否') . "\n";
    
    if (preg_match('/[^a-zA-Z0-9]/', $password)) {
        echo "⚠️  密码包含特殊字符，可能需要转义\n";
    }
}

echo "\n=== 建议 ===\n";
echo "1. 请在宝塔面板中确认数据库用户 '$username' 的密码\n";
echo "2. 确认用户权限设置（localhost vs %）\n";
echo "3. 如果问题持续，请尝试重置数据库密码\n";
