<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f8fff8; }
        .error { border-color: #f44336; background: #fff8f8; }
        .warning { border-color: #ff9800; background: #fffaf0; }
        .btn {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover { background: #337ab7; }
        .log {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 管理后台加载测试</h1>
        <p>此页面用于测试管理后台的加载问题和修复效果</p>
        
        <div class="test-item">
            <h3>1. 资源文件检查</h3>
            <button class="btn" onclick="checkResources()">检查资源文件</button>
            <div id="resource-log" class="log"></div>
        </div>
        
        <div class="test-item">
            <h3>2. Vue.js 模块加载测试</h3>
            <button class="btn" onclick="testVueLoading()">测试Vue加载</button>
            <div id="vue-log" class="log"></div>
        </div>
        
        <div class="test-item">
            <h3>3. 缓存清除</h3>
            <button class="btn" onclick="clearCache()">清除缓存</button>
            <button class="btn" onclick="hardRefresh()">强制刷新</button>
            <div id="cache-log" class="log"></div>
        </div>
        
        <div class="test-item">
            <h3>4. 管理后台访问</h3>
            <button class="btn" onclick="openAdmin()">打开管理后台</button>
            <button class="btn" onclick="openAdminNewTab()">新标签页打开</button>
            <div id="admin-log" class="log"></div>
        </div>
        
        <div class="test-item">
            <h3>5. 错误日志</h3>
            <button class="btn" onclick="showErrors()">显示错误</button>
            <button class="btn" onclick="clearErrors()">清除错误</button>
            <div id="error-log" class="log"></div>
        </div>
    </div>

    <script>
        let errors = [];
        
        // 捕获所有错误
        window.addEventListener('error', function(e) {
            errors.push({
                type: 'JavaScript Error',
                message: e.message,
                file: e.filename,
                line: e.lineno,
                time: new Date().toLocaleTimeString()
            });
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            errors.push({
                type: 'Promise Rejection',
                message: e.reason,
                time: new Date().toLocaleTimeString()
            });
        });
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            element.innerHTML += `<div style="color: ${color}">[${time}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }
        
        async function checkResources() {
            const logId = 'resource-log';
            document.getElementById(logId).innerHTML = '';
            
            const resources = [
                '/admin/assets/index-XVEiIDg_.css',
                '/admin/assets/index-BWOuGn0N.js',
                '/admin/assets/vue-vendor-B18IO7kr.js',
                '/admin/assets/element-plus-BNplCcqW.js',
                '/admin/vue-error-handler.js'
            ];
            
            log(logId, '开始检查资源文件...');
            
            for (const resource of resources) {
                try {
                    const response = await fetch(resource, { method: 'HEAD' });
                    if (response.ok) {
                        log(logId, `✅ ${resource} - OK (${response.status})`, 'success');
                    } else {
                        log(logId, `❌ ${resource} - 失败 (${response.status})`, 'error');
                    }
                } catch (error) {
                    log(logId, `❌ ${resource} - 网络错误: ${error.message}`, 'error');
                }
            }
            
            log(logId, '资源文件检查完成');
        }
        
        function testVueLoading() {
            const logId = 'vue-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始测试Vue.js模块加载...');
            
            // 创建一个临时的script标签来测试模块加载
            const script = document.createElement('script');
            script.type = 'module';
            script.innerHTML = `
                try {
                    import('/admin/assets/vue-vendor-B18IO7kr.js').then(module => {
                        console.log('Vue vendor模块加载成功');
                        window.postMessage({type: 'vue-test', status: 'success', message: 'Vue vendor模块加载成功'}, '*');
                    }).catch(error => {
                        console.error('Vue vendor模块加载失败:', error);
                        window.postMessage({type: 'vue-test', status: 'error', message: 'Vue vendor模块加载失败: ' + error.message}, '*');
                    });
                } catch (error) {
                    console.error('模块导入语法错误:', error);
                    window.postMessage({type: 'vue-test', status: 'error', message: '模块导入语法错误: ' + error.message}, '*');
                }
            `;
            
            // 监听测试结果
            window.addEventListener('message', function(event) {
                if (event.data.type === 'vue-test') {
                    if (event.data.status === 'success') {
                        log(logId, `✅ ${event.data.message}`, 'success');
                    } else {
                        log(logId, `❌ ${event.data.message}`, 'error');
                    }
                }
            });
            
            document.head.appendChild(script);
            
            setTimeout(() => {
                log(logId, 'Vue.js模块加载测试完成');
            }, 3000);
        }
        
        function clearCache() {
            const logId = 'cache-log';
            document.getElementById(logId).innerHTML = '';
            
            log(logId, '开始清除缓存...');
            
            // 清除localStorage
            try {
                localStorage.clear();
                log(logId, '✅ localStorage已清除', 'success');
            } catch (e) {
                log(logId, '❌ localStorage清除失败: ' + e.message, 'error');
            }
            
            // 清除sessionStorage
            try {
                sessionStorage.clear();
                log(logId, '✅ sessionStorage已清除', 'success');
            } catch (e) {
                log(logId, '❌ sessionStorage清除失败: ' + e.message, 'error');
            }
            
            // 尝试清除Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(function(registrations) {
                    for(let registration of registrations) {
                        registration.unregister();
                        log(logId, '✅ Service Worker已注销', 'success');
                    }
                });
            }
            
            log(logId, '缓存清除完成');
        }
        
        function hardRefresh() {
            const logId = 'cache-log';
            log(logId, '执行强制刷新...', 'warning');
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        }
        
        function openAdmin() {
            const logId = 'admin-log';
            document.getElementById(logId).innerHTML = '';
            log(logId, '正在打开管理后台...');
            window.location.href = '/admin/';
        }
        
        function openAdminNewTab() {
            const logId = 'admin-log';
            document.getElementById(logId).innerHTML = '';
            log(logId, '在新标签页打开管理后台...');
            window.open('/admin/', '_blank');
        }
        
        function showErrors() {
            const logId = 'error-log';
            document.getElementById(logId).innerHTML = '';
            
            if (errors.length === 0) {
                log(logId, '✅ 没有发现错误', 'success');
            } else {
                log(logId, `发现 ${errors.length} 个错误:`, 'warning');
                errors.forEach((error, index) => {
                    log(logId, `${index + 1}. [${error.time}] ${error.type}: ${error.message}`, 'error');
                    if (error.file) {
                        log(logId, `   文件: ${error.file}:${error.line}`, 'error');
                    }
                });
            }
        }
        
        function clearErrors() {
            errors = [];
            document.getElementById('error-log').innerHTML = '';
            log('error-log', '错误日志已清除', 'success');
        }
        
        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkResources, 1000);
        });
    </script>
</body>
</html>
