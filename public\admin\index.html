<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>

    <!-- 样式文件 -->
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">

    <!-- 强制忽略模块错误，直接加载应用 -->
    <script>
      // 全局错误抑制 - 专门处理 'xt' 初始化错误
      const originalError = window.onerror;
      window.onerror = function(msg, url, line, col, error) {
        // 如果是 'xt' 初始化错误，直接忽略
        if (msg && msg.includes('Cannot access') && msg.includes('xt') && msg.includes('before initialization')) {
          console.warn('🔇 已忽略 xt 初始化错误，继续运行应用');
          return true; // 阻止错误冒泡
        }
        // 其他错误正常处理
        if (originalError) {
          return originalError.apply(this, arguments);
        }
        return false;
      };

      // Promise错误抑制
      window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message &&
            event.reason.message.includes('Cannot access') &&
            event.reason.message.includes('xt') &&
            event.reason.message.includes('before initialization')) {
          console.warn('🔇 已忽略 Promise xt 初始化错误');
          event.preventDefault();
        }
      });

      // 监控应用状态
      let appCheckCount = 0;
      const maxAppChecks = 20;

      function checkAppStatus() {
        appCheckCount++;
        const app = document.getElementById('app');

        // 检查是否有Vue应用内容（不是加载指示器）
        if (app && app.children.length > 0) {
          const hasRealContent = !app.innerHTML.includes('正在加载') &&
                                !app.innerHTML.includes('系统维护') &&
                                app.innerHTML.trim() !== '';

          if (hasRealContent) {
            console.log('✅ Vue应用已成功挂载');
            return;
          }
        }

        // 如果超过检查次数，显示备用界面
        if (appCheckCount >= maxAppChecks) {
          console.warn('⚠️ 应用挂载超时，显示备用界面');
          showBackupInterface();
          return;
        }

        // 继续检查
        setTimeout(checkAppStatus, 500);
      }

      function showBackupInterface() {
        const app = document.getElementById('app');
        if (app) {
          app.innerHTML = `
            <div style="
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px;
            ">
              <div style="
                background: white;
                padding: 50px;
                border-radius: 16px;
                box-shadow: 0 25px 50px rgba(0,0,0,0.15);
                text-align: center;
                max-width: 600px;
                width: 100%;
              ">
                <div style="
                  width: 80px;
                  height: 80px;
                  background: linear-gradient(45deg, #ff9a9e, #fecfef);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 30px;
                  font-size: 36px;
                ">🚀</div>

                <h1 style="
                  color: #2c3e50;
                  margin: 0 0 20px 0;
                  font-size: 28px;
                  font-weight: 700;
                ">系统正在启动</h1>

                <p style="
                  color: #7f8c8d;
                  margin: 0 0 30px 0;
                  font-size: 16px;
                  line-height: 1.6;
                ">前端应用正在初始化，这可能需要一些时间。<br>如果长时间未响应，请尝试以下解决方案：</p>

                <div style="margin-bottom: 30px;">
                  <button onclick="forceReload()" style="
                    background: linear-gradient(45deg, #3498db, #2980b9);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0 10px 10px 0;
                    transition: all 0.3s;
                  " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    🔄 强制刷新
                  </button>

                  <button onclick="clearCacheAndReload()" style="
                    background: linear-gradient(45deg, #e74c3c, #c0392b);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0 10px 10px 0;
                    transition: all 0.3s;
                  " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    🧹 清除缓存
                  </button>

                  <button onclick="tryCompatibilityMode()" style="
                    background: linear-gradient(45deg, #f39c12, #e67e22);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0 10px 10px 0;
                    transition: all 0.3s;
                  " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    🔧 兼容模式
                  </button>
                </div>

                <div style="
                  background: #f8f9fa;
                  border-radius: 10px;
                  padding: 20px;
                  text-align: left;
                  font-size: 14px;
                  color: #6c757d;
                ">
                  <p style="margin: 0 0 10px 0; font-weight: 600;">💡 故障排除：</p>
                  <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>确保使用现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）</li>
                    <li>检查网络连接是否稳定</li>
                    <li>尝试禁用浏览器扩展</li>
                    <li>如果问题持续，请联系技术支持</li>
                  </ul>
                </div>
              </div>
            </div>

            <script>
              function forceReload() {
                window.location.reload(true);
              }

              function clearCacheAndReload() {
                sessionStorage.clear();
                localStorage.clear();
                if ('caches' in window) {
                  caches.keys().then(names => {
                    return Promise.all(names.map(name => caches.delete(name)));
                  }).then(() => {
                    window.location.reload(true);
                  }).catch(() => {
                    window.location.reload(true);
                  });
                } else {
                  window.location.reload(true);
                }
              }

              function tryCompatibilityMode() {
                // 设置兼容模式标记
                sessionStorage.setItem('compatibility_mode', 'true');
                window.location.reload(true);
              }
            </script>
          `;
        }
      }

      // 页面加载完成后开始监控
      document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM加载完成，开始监控应用状态...');

        // 检查是否是兼容模式
        if (sessionStorage.getItem('compatibility_mode') === 'true') {
          console.log('🔧 兼容模式：使用最小化加载');
          // 移除兼容模式标记
          sessionStorage.removeItem('compatibility_mode');
          // 使用最简单的方式加载
          document.head.innerHTML += '<script type="module" crossorigin src="/admin/assets/index-BWOuGn0N.js"><\/script>';
        }

        // 开始监控应用状态
        setTimeout(checkAppStatus, 1000);
      });
    </script>

    <!-- 直接加载主应用，忽略模块错误 -->
    <script type="module" crossorigin src="/admin/assets/index-BWOuGn0N.js"></script>
  </head>
  <body>
    <div id="app">
      <!-- 加载指示器 -->
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <div style="text-align: center;">
          <div style="width: 40px; height: 40px; border: 3px solid #f3f3f3; border-top: 3px solid #409EFF; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
          <div style="color: #606266; font-size: 14px;">正在加载管理后台...</div>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>
  </body>
</html>
