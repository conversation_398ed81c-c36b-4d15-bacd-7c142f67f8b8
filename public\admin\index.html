<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">

    <!-- 智能应用启动检测方案 -->
    <script>
      (function() {
        'use strict';

        // 错误抑制（保持静默）
        const originalError = window.onerror;
        window.onerror = function(msg, url, line, col, error) {
          if (msg && typeof msg === 'string' && msg.includes('Cannot access') && msg.includes('xt')) {
            return true;
          }
          return originalError ? originalError.apply(this, arguments) : false;
        };

        window.addEventListener('unhandledrejection', function(event) {
          if (event.reason && event.reason.message &&
              event.reason.message.includes('Cannot access') && event.reason.message.includes('xt')) {
            event.preventDefault();
          }
        });

        // 应用启动检测
        let startTime = Date.now();
        let checkCount = 0;
        const maxWaitTime = 15000; // 15秒超时
        const maxChecks = 30;

        function checkAppStatus() {
          checkCount++;
          const now = Date.now();
          const elapsed = now - startTime;

          const app = document.getElementById('app');
          if (!app) {
            console.warn('App容器不存在');
            return;
          }

          // 检查是否有Vue应用的迹象
          const hasVueContent = (
            app.children.length > 1 || // 有多个子元素
            app.querySelector('[class*="el-"]') || // Element Plus组件
            app.querySelector('[data-v-]') || // Vue组件
            app.querySelector('.router-view') || // 路由视图
            (app.innerHTML.length > 1000 && !app.innerHTML.includes('管理后台启动中')) // 内容丰富且不是加载页面
          );

          if (hasVueContent) {
            console.log('✅ Vue应用已成功启动');
            return;
          }

          // 超时处理
          if (elapsed > maxWaitTime || checkCount > maxChecks) {
            console.warn('⚠️ 应用启动超时，尝试恢复方案');
            showRecoveryOptions();
            return;
          }

          // 继续检查
          setTimeout(checkAppStatus, 500);
        }

        function showRecoveryOptions() {
          const app = document.getElementById('app');
          if (!app) return;

          app.innerHTML = `
            <div style="
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px;
            ">
              <div style="
                background: white;
                padding: 40px;
                border-radius: 16px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.15);
                text-align: center;
                max-width: 500px;
                width: 100%;
              ">
                <div style="
                  width: 80px;
                  height: 80px;
                  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 24px;
                  font-size: 40px;
                ">⚡</div>

                <h2 style="
                  color: #2c3e50;
                  margin: 0 0 16px 0;
                  font-size: 24px;
                  font-weight: 700;
                ">应用启动异常</h2>

                <p style="
                  color: #7f8c8d;
                  margin: 0 0 24px 0;
                  line-height: 1.6;
                ">前端应用无法正常启动，这可能是由于模块兼容性问题导致的。</p>

                <div style="margin-bottom: 24px;">
                  <button onclick="tryReload()" style="
                    background: #3498db;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    margin: 0 8px 8px 0;
                    transition: all 0.2s;
                  ">🔄 重新加载</button>

                  <button onclick="clearAllData()" style="
                    background: #e74c3c;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    margin: 0 8px 8px 0;
                    transition: all 0.2s;
                  ">🧹 清除缓存</button>

                  <button onclick="tryCompatMode()" style="
                    background: #f39c12;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 600;
                    margin: 0 8px 8px 0;
                    transition: all 0.2s;
                  ">🔧 兼容模式</button>
                </div>

                <details style="
                  background: #f8f9fa;
                  border-radius: 8px;
                  padding: 16px;
                  text-align: left;
                  font-size: 13px;
                  color: #6c757d;
                ">
                  <summary style="cursor: pointer; font-weight: 600; margin-bottom: 8px;">
                    💡 技术信息
                  </summary>
                  <p style="margin: 0; line-height: 1.5;">
                    <strong>问题：</strong>Vue.js 模块初始化失败<br>
                    <strong>原因：</strong>可能是 Vue 和 Element Plus 版本兼容性问题<br>
                    <strong>建议：</strong>使用现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）
                  </p>
                </details>
              </div>
            </div>

            <script>
              function tryReload() {
                window.location.reload(true);
              }

              function clearAllData() {
                try {
                  sessionStorage.clear();
                  localStorage.clear();
                  if ('caches' in window) {
                    caches.keys().then(names => {
                      return Promise.all(names.map(name => caches.delete(name)));
                    }).then(() => {
                      window.location.reload(true);
                    }).catch(() => {
                      window.location.reload(true);
                    });
                  } else {
                    window.location.reload(true);
                  }
                } catch (e) {
                  window.location.reload(true);
                }
              }

              function tryCompatMode() {
                // 尝试使用最简单的加载方式
                sessionStorage.setItem('use_simple_mode', 'true');
                window.location.reload(true);
              }
            </script>
          `;
        }

        // 页面加载完成后开始检测
        document.addEventListener('DOMContentLoaded', function() {
          console.log('🚀 开始检测应用启动状态...');

          // 检查是否使用简单模式
          if (sessionStorage.getItem('use_simple_mode') === 'true') {
            console.log('🔧 使用简单模式加载');
            sessionStorage.removeItem('use_simple_mode');
            // 移除现有脚本，使用最基本的方式
            const existingScripts = document.querySelectorAll('script[src*="index-BWOuGn0N.js"]');
            existingScripts.forEach(script => script.remove());

            // 延迟加载，给页面更多时间
            setTimeout(() => {
              const script = document.createElement('script');
              script.type = 'module';
              script.src = '/admin/assets/index-BWOuGn0N.js?t=' + Date.now();
              document.head.appendChild(script);
            }, 1000);
          }

          setTimeout(checkAppStatus, 2000); // 给应用更多启动时间
        });

        console.log('🛡️ 智能启动检测器已就绪');
      })();
    </script>

    <!-- 主应用脚本 -->
    <script type="module" crossorigin src="/admin/assets/index-BWOuGn0N.js"></script>
  </head>
  <body>
    <div id="app">
      <!-- 简洁的加载指示器 -->
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          background: rgba(255,255,255,0.95);
          padding: 40px;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          backdrop-filter: blur(10px);
        ">
          <div style="
            width: 50px;
            height: 50px;
            border: 3px solid rgba(64, 158, 255, 0.3);
            border-top: 3px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          "></div>

          <h3 style="
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
          ">晨鑫流量变现</h3>

          <p style="
            color: #7f8c8d;
            margin: 0;
            font-size: 14px;
          ">管理后台启动中...</p>
        </div>
      </div>

      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        body {
          margin: 0;
          padding: 0;
        }
      </style>
    </div>
  </body>
</html>
