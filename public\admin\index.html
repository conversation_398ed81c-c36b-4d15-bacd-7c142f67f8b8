<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    <!-- 防止缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 样式文件 -->
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">
    <!-- 错误处理脚本 -->
    <script src="/admin/vue-error-handler.js"></script>
  </head>
  <body>
    <div id="app">
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div style="text-align: center; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
          <div style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #409EFF; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
          <h3 style="color: #333; margin-bottom: 10px;">晨鑫流量变现</h3>
          <p style="color: #666; font-size: 16px; margin-bottom: 10px;">正在加载管理后台...</p>
          <p style="color: #999; font-size: 14px;">如果长时间未加载，请强制刷新页面 (Ctrl+F5)</p>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>

    <!-- 延迟加载主应用脚本 -->
    <script>
      // 确保DOM完全加载后再加载Vue应用
      document.addEventListener('DOMContentLoaded', function() {
        const script = document.createElement('script');
        script.type = 'module';
        script.crossOrigin = 'anonymous';
        script.src = '/admin/assets/index-BWOuGn0N.js';

        // 添加错误处理
        script.onerror = function() {
          console.error('主应用脚本加载失败，尝试重新加载...');
          setTimeout(function() {
            window.location.reload(true);
          }, 2000);
        };

        document.head.appendChild(script);
      });
    </script>
  </body>
</html>
