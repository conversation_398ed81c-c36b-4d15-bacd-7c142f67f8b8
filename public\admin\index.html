<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">

    <!-- 简化的错误处理和应用检测 -->
    <script>
      // 简化的错误处理
      window.onerror = function(msg) {
        if (msg && msg.includes('Cannot access') && msg.includes('xt')) return true;
        return false;
      };

      window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && e.reason.message.includes('xt')) {
          e.preventDefault();
        }
      });

      // 简单的超时检测
      let appStarted = false;

      function checkApp() {
        const app = document.getElementById('app');
        if (app && (
          app.querySelector('[class*="el-"]') ||
          app.querySelector('[data-v-]') ||
          (app.children.length > 1 && !app.innerHTML.includes('管理后台启动中'))
        )) {
          appStarted = true;
          console.log('✅ 应用启动成功');
        }
      }

      // 15秒后检查应用状态
      setTimeout(function() {
        if (!appStarted) {
          const app = document.getElementById('app');
          if (app && app.innerHTML.includes('管理后台启动中')) {
            app.innerHTML = `
              <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: Arial, sans-serif; padding: 20px;">
                <div style="background: white; padding: 40px; border-radius: 12px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.2); max-width: 500px;">
                  <div style="font-size: 48px; margin-bottom: 20px;">⚡</div>
                  <h2 style="color: #e74c3c; margin-bottom: 16px;">应用启动异常</h2>
                  <p style="color: #666; margin-bottom: 24px; line-height: 1.5;">前端应用无法正常启动，可能是模块兼容性问题</p>
                  <button onclick="location.reload(true)" style="background: #3498db; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 0 8px;">🔄 重新加载</button>
                  <button onclick="clearCache()" style="background: #e74c3c; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 0 8px;">🧹 清除缓存</button>
                </div>
              </div>
              <script>
                function clearCache() {
                  sessionStorage.clear();
                  localStorage.clear();
                  if ('caches' in window) {
                    caches.keys().then(names => Promise.all(names.map(name => caches.delete(name)))).then(() => location.reload(true));
                  } else {
                    location.reload(true);
                  }
                }
              </script>
            `;
          }
        }
      }, 15000);

      // 定期检查应用状态
      setInterval(checkApp, 1000);
    </script>

    <!-- 主应用脚本 -->
    <script type="module" crossorigin src="/admin/assets/index-BWOuGn0N.js"></script>
  </head>
  <body>
    <div id="app">
      <!-- 简洁的加载指示器 -->
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          background: rgba(255,255,255,0.95);
          padding: 40px;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          backdrop-filter: blur(10px);
        ">
          <div style="
            width: 50px;
            height: 50px;
            border: 3px solid rgba(64, 158, 255, 0.3);
            border-top: 3px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          "></div>

          <h3 style="
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
          ">晨鑫流量变现</h3>

          <p style="
            color: #7f8c8d;
            margin: 0;
            font-size: 14px;
          ">管理后台启动中...</p>
        </div>
      </div>

      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        body {
          margin: 0;
          padding: 0;
        }
      </style>
    </div>
  </body>
</html>
