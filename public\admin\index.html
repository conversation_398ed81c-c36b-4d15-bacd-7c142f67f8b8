<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>

    <!-- 样式文件 - 优先加载 -->
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">

    <!-- 模块预加载 - 严格按依赖顺序 -->
    <!-- 1. 首先加载 Vue 核心 -->
    <link rel="modulepreload" crossorigin href="/admin/assets/vue-vendor-B18IO7kr.js">
    <!-- 2. 然后加载工具库 -->
    <link rel="modulepreload" crossorigin href="/admin/assets/utils-Dt62F9fo.js">
    <!-- 3. 再加载 Element Plus（依赖 Vue） -->
    <link rel="modulepreload" crossorigin href="/admin/assets/element-plus-BNplCcqW.js">
    <!-- 4. 最后加载图表库 -->
    <link rel="modulepreload" crossorigin href="/admin/assets/echarts-A7wIXSPL.js">

    <!-- 模块加载修复脚本 - 优先加载 -->
    <script src="/admin/module-loader-fix.js"></script>

    <!-- 主应用脚本 - 延迟加载确保所有依赖就绪 -->
    <script type="module" crossorigin src="/admin/assets/index-BWOuGn0N.js" defer></script>
  </head>
  <body>
    <div id="app">
      <!-- 加载指示器 -->
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <div style="text-align: center;">
          <div style="width: 40px; height: 40px; border: 3px solid #f3f3f3; border-top: 3px solid #409EFF; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
          <div style="color: #606266; font-size: 14px;">正在加载管理后台...</div>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>
  </body>
</html>
