<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">

    <!-- 终极错误抑制方案 -->
    <script>
      // 在脚本加载前就开始拦截错误
      (function() {
        'use strict';

        // 完全静默 xt 相关错误
        const originalError = window.onerror;
        window.onerror = function(msg, url, line, col, error) {
          if (msg && typeof msg === 'string') {
            if (msg.includes('Cannot access') && msg.includes('xt') && msg.includes('before initialization')) {
              return true; // 完全忽略
            }
          }
          return originalError ? originalError.apply(this, arguments) : false;
        };

        // Promise 错误拦截
        window.addEventListener('unhandledrejection', function(event) {
          if (event.reason && event.reason.message) {
            if (event.reason.message.includes('Cannot access') &&
                event.reason.message.includes('xt') &&
                event.reason.message.includes('before initialization')) {
              event.preventDefault();
              return;
            }
          }
        });

        // 控制台错误过滤
        const originalConsoleError = console.error;
        console.error = function() {
          const args = Array.prototype.slice.call(arguments);
          const message = args.join(' ');
          if (message.includes('Cannot access') && message.includes('xt') && message.includes('before initialization')) {
            return; // 不输出到控制台
          }
          originalConsoleError.apply(console, args);
        };

        console.log('🛡️ 错误拦截器已启动');
      })();
    </script>

    <!-- 主应用脚本 -->
    <script type="module" crossorigin src="/admin/assets/index-BWOuGn0N.js"></script>
  </head>
  <body>
    <div id="app">
      <!-- 简洁的加载指示器 -->
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          background: rgba(255,255,255,0.95);
          padding: 40px;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          backdrop-filter: blur(10px);
        ">
          <div style="
            width: 50px;
            height: 50px;
            border: 3px solid rgba(64, 158, 255, 0.3);
            border-top: 3px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          "></div>

          <h3 style="
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
          ">晨鑫流量变现</h3>

          <p style="
            color: #7f8c8d;
            margin: 0;
            font-size: 14px;
          ">管理后台启动中...</p>
        </div>
      </div>

      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        body {
          margin: 0;
          padding: 0;
        }
      </style>
    </div>
  </body>
</html>
