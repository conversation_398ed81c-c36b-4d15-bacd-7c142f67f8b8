<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>

    <!-- 样式文件 -->
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">

    <!-- 使用动态加载避免模块初始化冲突 -->
    <script>
      // 禁用模块预加载，使用动态导入
      window.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 开始动态加载模块...');

        // 延迟加载主应用，给浏览器更多时间准备
        setTimeout(function() {
          const script = document.createElement('script');
          script.type = 'module';
          script.crossOrigin = 'anonymous';
          script.src = '/admin/assets/index-BWOuGn0N.js';

          script.onload = function() {
            console.log('✅ 主应用加载成功');
          };

          script.onerror = function(e) {
            console.error('❌ 主应用加载失败:', e);
            showFallbackUI();
          };

          document.head.appendChild(script);
        }, 200); // 增加延迟时间
      });

      // 显示备用界面
      function showFallbackUI() {
        document.getElementById('app').innerHTML = `
          <div style="display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
            <div style="background: white; padding: 40px; border-radius: 12px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 500px;">
              <div style="font-size: 48px; margin-bottom: 20px;">🔧</div>
              <h2 style="color: #e74c3c; margin-bottom: 16px;">系统维护中</h2>
              <p style="color: #666; margin-bottom: 24px;">前端资源正在优化，请稍后再试</p>
              <button onclick="location.reload(true)" style="background: #409EFF; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">重新加载</button>
            </div>
          </div>
        `;
      }
    </script>
  </head>
  <body>
    <div id="app">
      <!-- 加载指示器 -->
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
        <div style="text-align: center;">
          <div style="width: 40px; height: 40px; border: 3px solid #f3f3f3; border-top: 3px solid #409EFF; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
          <div style="color: #606266; font-size: 14px;">正在加载管理后台...</div>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>
  </body>
</html>
