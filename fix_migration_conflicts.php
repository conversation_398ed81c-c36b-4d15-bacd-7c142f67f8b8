<?php
/**
 * 智能迁移冲突修复脚本
 * 专门处理数据库迁移中的表和字段冲突问题
 */

require_once __DIR__ . '/vendor/autoload.php';

class MigrationConflictFixer
{
    private $pdo;
    private $projectRoot;
    
    public function __construct()
    {
        $this->projectRoot = __DIR__;
        $this->loadEnvironment();
        $this->connectDatabase();
    }
    
    private function loadEnvironment()
    {
        $envFile = $this->projectRoot . '/.env';
        if (!file_exists($envFile)) {
            throw new Exception('.env 文件不存在');
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value);
            }
        }
    }
    
    private function connectDatabase()
    {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? '';
        $username = $_ENV['DB_USERNAME'] ?? '';
        $password = $_ENV['DB_PASSWORD'] ?? '';
        
        $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
        
        try {
            $this->pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            echo "✅ 数据库连接成功\n";
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    public function fixMigrationConflicts()
    {
        echo "🔧 开始检查和修复迁移冲突...\n\n";
        
        // 1. 获取现有表
        $existingTables = $this->getExistingTables();
        echo "📊 发现 " . count($existingTables) . " 个现有数据表\n";
        
        // 2. 获取待执行迁移
        $pendingMigrations = $this->getPendingMigrations();
        if (empty($pendingMigrations)) {
            echo "✅ 没有待执行的迁移\n";
            return true;
        }
        
        echo "📋 发现 " . count($pendingMigrations) . " 个待执行迁移\n\n";
        
        // 3. 分析冲突
        $conflicts = $this->analyzeConflicts($pendingMigrations, $existingTables);
        
        if (empty($conflicts)) {
            echo "✅ 没有发现迁移冲突\n";
            return true;
        }
        
        // 4. 修复冲突
        return $this->resolveConflicts($conflicts);
    }
    
    private function getExistingTables()
    {
        $stmt = $this->pdo->query("SHOW TABLES");
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    private function getPendingMigrations()
    {
        $output = [];
        exec('php artisan migrate:status 2>&1', $output);
        
        $pending = [];
        foreach ($output as $line) {
            if (preg_match('/^\s*([^\s]+)\s+.*Pending\s*$/', $line, $matches)) {
                $pending[] = trim($matches[1]);
            }
        }
        
        return $pending;
    }
    
    private function analyzeConflicts($pendingMigrations, $existingTables)
    {
        $conflicts = [];
        
        foreach ($pendingMigrations as $migration) {
            $migrationFile = $this->projectRoot . '/database/migrations/' . $migration . '.php';
            
            if (!file_exists($migrationFile)) {
                echo "⚠️  迁移文件不存在: $migration\n";
                continue;
            }
            
            $content = file_get_contents($migrationFile);
            
            // 检查表创建冲突
            if (preg_match_all('/Schema::create\([\'"]([^\'"]+)[\'"]/', $content, $matches)) {
                foreach ($matches[1] as $tableName) {
                    if (in_array($tableName, $existingTables)) {
                        $conflicts[] = [
                            'migration' => $migration,
                            'type' => 'create_table',
                            'table' => $tableName,
                            'file' => $migrationFile
                        ];
                        echo "❌ 表冲突: $migration 试图创建已存在的表 '$tableName'\n";
                    }
                }
            }
            
            // 检查字段添加冲突
            if (preg_match_all('/Schema::table\([\'"]([^\'"]+)[\'"]/', $content, $tableMatches)) {
                foreach ($tableMatches[1] as $tableName) {
                    if (in_array($tableName, $existingTables)) {
                        $columnConflicts = $this->checkColumnConflicts($content, $tableName);
                        if (!empty($columnConflicts)) {
                            $conflicts[] = [
                                'migration' => $migration,
                                'type' => 'add_column',
                                'table' => $tableName,
                                'columns' => $columnConflicts,
                                'file' => $migrationFile
                            ];
                            echo "❌ 字段冲突: $migration 在表 '$tableName' 中添加已存在的字段: " . implode(', ', $columnConflicts) . "\n";
                        }
                    }
                }
            }
        }
        
        return $conflicts;
    }
    
    private function checkColumnConflicts($content, $tableName)
    {
        try {
            $stmt = $this->pdo->query("SHOW COLUMNS FROM `{$tableName}`");
            $existingColumns = [];
            while ($row = $stmt->fetch()) {
                $existingColumns[] = $row['Field'];
            }
            
            $conflicts = [];
            if (preg_match_all('/\$table->[\w]+\([\'"]([^\'"]+)[\'"]/', $content, $columnMatches)) {
                foreach ($columnMatches[1] as $columnName) {
                    if (in_array($columnName, $existingColumns)) {
                        $conflicts[] = $columnName;
                    }
                }
            }
            
            return $conflicts;
        } catch (Exception $e) {
            return [];
        }
    }
    
    private function resolveConflicts($conflicts)
    {
        echo "\n🔧 开始修复冲突...\n";
        
        // 确保 migrations 表存在
        $this->ensureMigrationsTable();
        
        $fixed = 0;
        foreach ($conflicts as $conflict) {
            switch ($conflict['type']) {
                case 'create_table':
                    if ($this->markMigrationAsRun($conflict['migration'])) {
                        echo "✅ 已标记迁移为完成: {$conflict['migration']} (表 {$conflict['table']} 已存在)\n";
                        $fixed++;
                    }
                    break;
                    
                case 'add_column':
                    if ($this->markMigrationAsRun($conflict['migration'])) {
                        echo "✅ 已标记迁移为完成: {$conflict['migration']} (字段已存在)\n";
                        $fixed++;
                    }
                    break;
            }
        }
        
        echo "\n📊 修复统计: 共修复 $fixed 个冲突\n";
        
        // 重新尝试迁移
        echo "\n🚀 重新执行迁移...\n";
        $output = [];
        exec('php artisan migrate --force 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ 迁移执行成功！\n";
            return true;
        } else {
            echo "❌ 迁移仍然失败:\n";
            foreach ($output as $line) {
                echo "   $line\n";
            }
            return false;
        }
    }
    
    private function ensureMigrationsTable()
    {
        try {
            $this->pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL,
                batch INT NOT NULL
            )");
        } catch (Exception $e) {
            echo "⚠️  创建 migrations 表失败: " . $e->getMessage() . "\n";
        }
    }
    
    private function markMigrationAsRun($migration)
    {
        try {
            // 获取最大批次号
            $stmt = $this->pdo->query("SELECT MAX(batch) as max_batch FROM migrations");
            $result = $stmt->fetch();
            $batch = ($result['max_batch'] ?? 0) + 1;
            
            // 插入迁移记录
            $stmt = $this->pdo->prepare("INSERT IGNORE INTO migrations (migration, batch) VALUES (?, ?)");
            return $stmt->execute([$migration, $batch]);
        } catch (Exception $e) {
            echo "❌ 标记迁移失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// 执行修复
try {
    $fixer = new MigrationConflictFixer();
    $success = $fixer->fixMigrationConflicts();
    
    if ($success) {
        echo "\n🎉 迁移冲突修复完成！\n";
        exit(0);
    } else {
        echo "\n💥 迁移冲突修复失败！\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "💥 错误: " . $e->getMessage() . "\n";
    exit(1);
}
