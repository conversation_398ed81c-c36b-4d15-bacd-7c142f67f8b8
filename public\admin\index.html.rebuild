<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/admin/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>晨鑫流量变现系统 管理后台</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" crossorigin href="/admin/assets/index-XVEiIDg_.css">
    
    <!-- 完全避免模块预加载，使用传统方式 -->
    <script>
      // 全局错误处理
      window.onerror = function(msg, url, line, col, error) {
        console.error('全局错误:', { msg, url, line, col, error });
        if (msg.includes('Cannot access') && msg.includes('before initialization')) {
          console.warn('检测到模块初始化错误，使用备用方案');
          return true; // 阻止默认错误处理
        }
      };
      
      // Promise错误处理
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Promise错误:', event.reason);
        if (event.reason && event.reason.message && 
            event.reason.message.includes('Cannot access') && 
            event.reason.message.includes('before initialization')) {
          console.warn('检测到Promise模块错误，使用备用方案');
          event.preventDefault();
        }
      });
      
      // 智能加载策略
      let loadAttempts = 0;
      const maxAttempts = 3;
      
      function loadApplication() {
        loadAttempts++;
        console.log(`🚀 尝试加载应用 (${loadAttempts}/${maxAttempts})`);
        
        // 清理之前的脚本
        const existingScripts = document.querySelectorAll('script[src*="index-BWOuGn0N.js"]');
        existingScripts.forEach(script => script.remove());
        
        const script = document.createElement('script');
        script.type = 'module';
        script.crossOrigin = 'anonymous';
        script.src = '/admin/assets/index-BWOuGn0N.js?v=' + Date.now(); // 添加时间戳避免缓存
        
        script.onload = function() {
          console.log('✅ 应用加载成功');
          // 监听应用是否真正启动
          setTimeout(checkAppStatus, 2000);
        };
        
        script.onerror = function(e) {
          console.error('❌ 应用加载失败:', e);
          handleLoadFailure();
        };
        
        document.head.appendChild(script);
      }
      
      function checkAppStatus() {
        const app = document.getElementById('app');
        const hasVueApp = app && app.children.length > 0 && 
                         !app.innerHTML.includes('正在加载') && 
                         !app.innerHTML.includes('系统维护');
        
        if (hasVueApp) {
          console.log('✅ Vue应用启动成功');
        } else if (loadAttempts < maxAttempts) {
          console.warn('⚠️ 应用未正常启动，重试...');
          setTimeout(loadApplication, 1000 * loadAttempts); // 递增延迟
        } else {
          console.error('❌ 应用启动失败，显示维护页面');
          showMaintenancePage();
        }
      }
      
      function handleLoadFailure() {
        if (loadAttempts < maxAttempts) {
          console.log(`🔄 ${2000 * loadAttempts}ms后重试...`);
          setTimeout(loadApplication, 2000 * loadAttempts);
        } else {
          showMaintenancePage();
        }
      }
      
      function showMaintenancePage() {
        const app = document.getElementById('app');
        if (app) {
          app.innerHTML = `
            <div style="
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              padding: 20px;
            ">
              <div style="
                background: white;
                padding: 50px;
                border-radius: 16px;
                box-shadow: 0 25px 50px rgba(0,0,0,0.15);
                text-align: center;
                max-width: 600px;
                width: 100%;
              ">
                <div style="
                  width: 100px;
                  height: 100px;
                  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 30px;
                  font-size: 48px;
                ">🔧</div>
                
                <h1 style="
                  color: #2c3e50;
                  margin: 0 0 20px 0;
                  font-size: 28px;
                  font-weight: 700;
                ">系统维护中</h1>
                
                <p style="
                  color: #7f8c8d;
                  margin: 0 0 30px 0;
                  font-size: 18px;
                  line-height: 1.6;
                ">前端模块正在优化升级，为您提供更好的使用体验</p>
                
                <div style="margin-bottom: 30px;">
                  <button onclick="forceReload()" style="
                    background: linear-gradient(45deg, #3498db, #2980b9);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0 10px 10px 0;
                    transition: transform 0.2s;
                  " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    🔄 重新加载
                  </button>
                  
                  <button onclick="clearAllCache()" style="
                    background: linear-gradient(45deg, #e74c3c, #c0392b);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0 10px 10px 0;
                    transition: transform 0.2s;
                  " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    🧹 清除缓存
                  </button>
                </div>
                
                <details style="
                  text-align: left;
                  background: #f8f9fa;
                  border-radius: 10px;
                  padding: 20px;
                  margin-top: 30px;
                ">
                  <summary style="
                    cursor: pointer;
                    font-weight: 600;
                    color: #495057;
                    font-size: 16px;
                    margin-bottom: 15px;
                  ">💡 故障排除指南</summary>
                  
                  <div style="color: #6c757d; font-size: 14px; line-height: 1.6;">
                    <p><strong>如果问题持续存在，请尝试：</strong></p>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                      <li>按 <kbd style="background: #e9ecef; padding: 2px 6px; border-radius: 3px;">Ctrl+F5</kbd> 强制刷新页面</li>
                      <li>清除浏览器缓存和Cookie</li>
                      <li>尝试使用无痕/隐私模式访问</li>
                      <li>检查网络连接是否正常</li>
                      <li>尝试使用其他浏览器（Chrome、Firefox、Edge）</li>
                      <li>联系技术支持获取帮助</li>
                    </ol>
                    <p style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                      <strong>技术信息：</strong><br>
                      错误类型：前端模块加载冲突<br>
                      建议浏览器：Chrome 90+, Firefox 88+, Safari 14+
                    </p>
                  </div>
                </details>
              </div>
            </div>
            
            <script>
              function forceReload() {
                // 清除会话存储
                sessionStorage.clear();
                // 强制刷新
                window.location.reload(true);
              }
              
              function clearAllCache() {
                // 清除所有存储
                sessionStorage.clear();
                localStorage.clear();
                
                // 清除缓存API
                if ('caches' in window) {
                  caches.keys().then(function(names) {
                    return Promise.all(names.map(name => caches.delete(name)));
                  }).then(function() {
                    window.location.reload(true);
                  }).catch(function() {
                    window.location.reload(true);
                  });
                } else {
                  window.location.reload(true);
                }
              }
            </script>
          `;
        }
      }
      
      // 页面加载完成后开始加载应用
      document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM加载完成，准备加载应用...');
        // 给浏览器一些时间来稳定
        setTimeout(loadApplication, 300);
      });
    </script>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载指示器 -->
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <div style="
          background: white;
          padding: 40px;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          text-align: center;
          min-width: 320px;
        ">
          <div style="
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #409EFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 24px;
          "></div>
          
          <h3 style="
            color: #333;
            margin: 0 0 12px 0;
            font-size: 22px;
            font-weight: 600;
          ">晨鑫流量变现</h3>
          
          <p style="
            color: #666;
            margin: 0 0 16px 0;
            font-size: 16px;
          ">正在启动管理后台...</p>
          
          <div style="
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 16px;
          ">
            <div style="
              width: 0%;
              height: 100%;
              background: linear-gradient(90deg, #409EFF, #67C23A);
              border-radius: 2px;
              animation: progress 3s ease-in-out infinite;
            "></div>
          </div>
          
          <p style="
            color: #999;
            margin: 0;
            font-size: 12px;
          ">首次加载可能需要较长时间，请耐心等待</p>
        </div>
      </div>
      
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes progress {
          0% { width: 0%; }
          50% { width: 70%; }
          100% { width: 100%; }
        }
        
        body {
          margin: 0;
          padding: 0;
        }
        
        kbd {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 3px;
          padding: 2px 6px;
          font-family: monospace;
          font-size: 12px;
        }
      </style>
    </div>
  </body>
</html>
